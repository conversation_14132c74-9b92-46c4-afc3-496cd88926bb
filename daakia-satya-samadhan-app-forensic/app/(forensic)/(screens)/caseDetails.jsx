import React, { useState, useEffect } from "react";
import {
  View,
  Text,
  TouchableOpacity,
  Image,
  StyleSheet,
  FlatList,
  ActivityIndicator,
  Alert,
  Modal,
  Platform,
  TextInput,
} from "react-native";
import MaterialCommunityIcons from "@expo/vector-icons/MaterialCommunityIcons";
import { GestureHandlerRootView } from 'react-native-gesture-handler';
import Constants from 'expo-constants';
import * as FileSystem from 'expo-file-system';
import * as IntentLauncher from 'expo-intent-launcher';
import * as Sharing from 'expo-sharing';
import * as WebBrowser from 'expo-web-browser';
import { useAuth } from "../../../context/auth-context";
import { router, useLocalSearchParams } from "expo-router";
import { transformUrl } from "../../../utils/transformUrl";
import { Colors } from "../../../constants/colors";
import PreviewComponent from '../../../components/Larges/PreviewComponent';
import ReportsTab from '../../../components/ReportsTab';
import EvidenceCard from '../../../components/EvidenceCard';
import EvidenceAcceptanceModal from '../../../components/Larges/EvidenceAcceptanceModal';
import SegmentedControl from '../../../components/Smalls/SegmentedControl';
import { apiService } from '../../../services/api';
import { validationService } from '../../../services/validation';

const BASE_URL = Constants.expoConfig.extra.baseUrl;


const ForensicRequestDetails = () => {
  const { token } = useAuth();
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState(null);
  const [requestData, setRequestData] = useState(null);
  const { forensicRequestId } = useLocalSearchParams();
  const [activeTab, setActiveTab] = useState('evidences');
  const [reports, setReports] = useState([]);
  const [reportPermissions, setReportPermissions] = useState({});
  const [showPreview, setShowPreview] = useState(false);
  const [previewUri, setPreviewUri] = useState(null);
  const [downloadingId, setDownloadingId] = useState(null);
  const [showPermissionModal, setShowPermissionModal] = useState(false);
  const [selectedReport, setSelectedReport] = useState(null);
  const [accessDuration, setAccessDuration] = useState('24');
  const [showAccessRequestModal, setShowAccessRequestModal] = useState(false);
  const [reportAccessRequests, setReportAccessRequests] = useState({});
  const [showEvidenceAcceptanceModal, setShowEvidenceAcceptanceModal] = useState(false);

  useEffect(() => {
    fetchForensicRequestDetails();
  }, [forensicRequestId]);

  useEffect(() => {
    if (requestData?.caseId?._id) {
      fetchReports();
    }
  }, [requestData]);

  // Show Evidence Acceptance Modal based on status
  useEffect(() => {
    if (requestData?.status) {
      const modalVisibleStatuses = ['dispatched', 'rejected', 'initiated'];
      const shouldShowModal = modalVisibleStatuses.includes(requestData.status.toLowerCase());
      setShowEvidenceAcceptanceModal(shouldShowModal);
    }
  }, [requestData?.status]);

  const fetchAccessRequests = async (reportId) => {
    try {
      validationService.validateReportId(reportId);
      const result = await apiService.fetchReportAccess(token, reportId);
      
      if (result.status === 'success') {
        // Group requests by userId and keep only the latest one
        const uniqueRequests = result.data.reduce((acc, request) => {
          const userId = request.userId._id;
          
          // If this is the first request for this user or it's newer than existing one
          if (!acc[userId] || new Date(request.createdAt) > new Date(acc[userId].createdAt)) {
            acc[userId] = request;
          }
          return acc;
        }, {});

        // Convert back to array
        const filteredRequests = Object.values(uniqueRequests);

        // Update the access requests state
        setReportAccessRequests(prev => ({
          ...prev,
          [reportId]: filteredRequests
        }));

        const formattedData = {
          reportId,
          totalRequests: filteredRequests.length,
          requests: filteredRequests.map(req => ({
            id: req._id,
            access: req.access,
            createdAt: req.createdAt,
            userId: req.userId
          }))
        };
      }
      return result;
    } catch (err) {
      return null;
    }
  };

  const fetchReports = async () => {
    if (!requestData?.caseId?._id) return;
    
    try {
      validationService.validateCaseId(requestData.caseId._id);
      const result = await apiService.fetchCaseReports(token, requestData.caseId._id);
      
      if (result.status === 'success') {
        const processedReports = (result.data || []).map(report => {
          if (!report.evidenceId && report.evidenceDetails) {
            report.evidenceId = report.evidenceDetails._id;
          }
          
          if (!report.evidenceId && report.url && report.url.includes('evidence/')) {
            const matches = report.url.match(/evidence\/([^\/]+)/);
            if (matches && matches[1]) {
              report.evidenceId = matches[1];
            }
          }
          
          return report;
        });
        
        setReports(processedReports);
        
        const initialPermissions = {};
        processedReports.forEach(report => {
          initialPermissions[report._id] = 'granted';
        });
        setReportPermissions(initialPermissions);

        // Fetch access requests for each report
        processedReports.forEach(report => {
          fetchAccessRequests(report._id);
        });
      }
    } catch (err) {
      // Handle error silently
    }
  };

  const fetchForensicRequestDetails = async () => {
    setIsLoading(true);
    try {
      validationService.validateForensicRequestId(forensicRequestId);
      const result = await apiService.fetchForensicRequestEvidences(token, forensicRequestId);
      
      if (result.status === 'success') {
        setRequestData(result.data);
      } else {
        setError(result.message || 'Failed to fetch request details');
      }
    } catch (err) {
      setError('Network error: ' + err.message);
    } finally {
      setIsLoading(false);
    }
  };

  const handleSharePress = (evidenceId) => {
    router.replace({
      pathname: '(screens)/captureReport',
      params: { 
        evidenceId,
        caseId: requestData?.caseId?._id,
        forensicRequestId
      }
    });
  };

  const handleReportClick = () => {
    setActiveTab('reports');
    fetchReports();
  };

  const handleEvidenceClick = () => {
    setActiveTab('evidences');
  };

  const handleAttachmentClick = (url, title) => {
    if (!url) {
      Alert.alert('Error', 'No file URL provided');
      return;
    }

    const isPdf = url.toLowerCase().endsWith('.pdf');
    const isImageFile = /\.(jpg|jpeg|png|gif|webp)$/i.test(url);

    if (isPdf) {
      downloadAndOpenPdf(url, title);
    } else if (isImageFile) {
      setPreviewUri(transformUrl(url));
      setShowPreview(true);
    } else {
      const transformedUrl = transformUrl(url);
      WebBrowser.openBrowserAsync(transformedUrl);
    }
  };

  const closePreview = () => {
    setShowPreview(false);
    setPreviewUri(null);
  };

  const handleShowAccessRequests = (report) => {
    setSelectedReport(report);
    setShowAccessRequestModal(true);
  };

  const handleEvidencePress = (evidenceId) => {
    router.push({
      pathname: '(screens)/evidancesDetails',
      params: {
        evidenceId,
        caseId: requestData?.caseId?._id,
        forensicRequestId
      }
    });
  };

  // Handle evidence acceptance
  const handleEvidenceAccept = (updatedData) => {
    // Update the local state with the new status
    if (updatedData) {
      setRequestData(prev => ({
        ...prev,
        status: 'received',
        updatedAt: updatedData.updatedAt || new Date().toISOString()
      }));
    }
    setShowEvidenceAcceptanceModal(false);

    // Show success message
    Alert.alert(
      'Success',
      'Evidence has been accepted successfully. You can now create a report.',
      [{ text: 'OK' }]
    );
  };

  // Handle evidence rejection
  const handleEvidenceReject = (updatedData) => {
    // Update the local state with the new status
    if (updatedData) {
      setRequestData(prev => ({
        ...prev,
        status: 'rejected',
        updatedAt: updatedData.updatedAt || new Date().toISOString()
      }));
    }
    setShowEvidenceAcceptanceModal(false);

    // Show success message
    Alert.alert(
      'Success',
      'Evidence has been rejected successfully.',
      [{ text: 'OK' }]
    );
  };

  // Handle modal close
  const handleEvidenceModalClose = () => {
    setShowEvidenceAcceptanceModal(false);
    // Navigate back when modal is closed
    router.back();
  };

  const handleApproveRequest = async (requestId) => {
    try {
      setIsLoading(true);
      
      // Calculate end date based on duration (in hours)
      const endDate = new Date();
      endDate.setHours(endDate.getHours() + parseInt(accessDuration));

      validationService.validateAccessRequest(requestId, 'granted', accessDuration);

      const result = await apiService.updateReportAccess(token, {
        access: 'granted',
        reportAccessId: requestId,
        duration: endDate.toISOString(),
      });

      if (result.status === 'success' && result.data) {
        // Update the local state with the new access status
        if (selectedReport && reportAccessRequests[selectedReport._id]) {
          const updatedRequests = reportAccessRequests[selectedReport._id].map(req => {
            if (req._id === requestId) {
              return {
                ...req,
                access: result.data.access,
                duration: result.data.duration,
                updatedAt: result.data.updatedAt
              };
            }
            return req;
          });
          
          setReportAccessRequests(prev => ({
            ...prev,
            [selectedReport._id]: updatedRequests
          }));
        }
      }
    } catch (error) {
      // Handle error silently
    } finally {
      setIsLoading(false);
    }
  };

  const handleRejectRequest = async (requestId) => {
    try {
      setIsLoading(true);

      validationService.validateAccessRequest(requestId, 'rejected');

      const result = await apiService.updateReportAccess(token, {
        access: 'rejected',
        reportAccessId: requestId,
        duration: null,
      });

      if (result.status === 'success' && result.data) {
        // Update the local state with the new access status
        if (selectedReport && reportAccessRequests[selectedReport._id]) {
          const updatedRequests = reportAccessRequests[selectedReport._id].map(req => {
            if (req._id === requestId) {
              return {
                ...req,
                access: result.data.access,
                updatedAt: result.data.updatedAt
              };
            }
            return req;
          });
          
          setReportAccessRequests(prev => ({
            ...prev,
            [selectedReport._id]: updatedRequests
          }));
        }
      }
    } catch (error) {
      // Handle error silently
    } finally {
      setIsLoading(false);
    }
  };

  // Functions for handling report attachments
  const downloadAndOpenPdf = async (url, title) => {
    if (!url) {
      Alert.alert('Error', 'No file URL provided');
      return;
    }
    
    try {
      setDownloadingId(`${title}-${url}`); // Unique ID for each URL
      const transformedUrl = transformUrl(url);
      const filename = url.split('/').pop();
      const localUri = `${FileSystem.documentDirectory}${filename}`;
      
      const downloadResumable = FileSystem.createDownloadResumable(
        transformedUrl,
        localUri,
        {},
        (downloadProgress) => {
          const progress = downloadProgress.totalBytesWritten / downloadProgress.totalBytesExpectedToWrite;
        }
      );
      
      const { uri } = await downloadResumable.downloadAsync();
      
      if (Platform.OS === 'ios') {
        const canShare = await Sharing.isAvailableAsync();
        if (canShare) {
          await Sharing.shareAsync(uri);
        } else {
          await WebBrowser.openBrowserAsync(transformedUrl);
        }
      } else if (Platform.OS === 'android') {
        const contentUri = await FileSystem.getContentUriAsync(uri);
        await IntentLauncher.startActivityAsync('android.intent.action.VIEW', {
          data: contentUri,
          flags: 1,
          type: 'application/pdf',
        });
      }
    } catch (error) {
      Alert.alert('Error', 'Could not download or open the PDF file');
    } finally {
      setDownloadingId(null);
    }
  };

  const renderItem = ({ item }) => {
    switch (item.type) {
      case 'caseDetails':
        return (
          <View style={styles.detailsContainer}>
            {[
              { label: 'Created At', value: new Date(requestData.caseId.createdAt).toLocaleString() },
              { label: 'Title', value: requestData.caseId.title },
              { label: 'Description', value: requestData.caseId.description },
              { label: 'Case Type', value: requestData.caseId.caseType },
              { label: 'Remarks', value: requestData.caseId.remarks },
              { label: 'Police Station', value: requestData.policeStationId?.name || 'Not Assigned' },
            ].map((detail, index) => (
              <View style={styles.detailRow} key={index}>
                <Text style={styles.label}>{detail.label}</Text>
                <Text style={styles.value}>{detail.value}</Text>
              </View>
            ))}
          </View>
        );
      case 'evidences':
        return (
          <View style={styles.sectionContainer}>
            <Text style={styles.sectionTitle}>Evidences</Text>
            
            <SegmentedControl
              segments={[
                {
                  key: 'evidences',
                  title: 'Evidences',
                  icon: 'file-document-multiple-outline'
                },
                {
                  key: 'reports',
                  title: 'Reports',
                  icon: 'clipboard-text-outline'
                }
              ]}
              activeSegment={activeTab}
              onSegmentPress={(segmentKey) => {
                if (segmentKey === 'evidences') {
                  handleEvidenceClick();
                } else if (segmentKey === 'reports') {
                  handleReportClick();
                }
              }}
            />
            
            {activeTab === 'evidences' ? (
              requestData.evidence ? (
                <EvidenceCard
                  evidence={requestData.evidence}
                  onPress={() => handleEvidencePress(requestData.evidence._id)}
                  onSharePress={() => handleSharePress(requestData.evidence._id)}
                />
              ) : (
                <Text style={styles.noEvidenceText}>No evidence found</Text>
              )
            ) : (
              <ReportsTab
                reports={reports}
                reportPermissions={reportPermissions}
                reportAccessRequests={reportAccessRequests}
                onShowAccessRequests={handleShowAccessRequests}
                onAttachmentClick={handleAttachmentClick}
                downloadingId={downloadingId}
                showPreview={showPreview}
                previewUri={previewUri}
                onClosePreview={closePreview}
                showAccessRequestModal={showAccessRequestModal}
                onCloseAccessRequestModal={() => setShowAccessRequestModal(false)}
                accessDuration={accessDuration}
                setAccessDuration={setAccessDuration}
                selectedReport={selectedReport}
                onApproveRequest={handleApproveRequest}
                onRejectRequest={handleRejectRequest}
                isLoading={isLoading}
                onRefresh={fetchReports}
              />
            )}
          </View>
        );
      default:
        return null;
    }
  };

  // Error display
  if (error) {
    return (
      <View style={styles.errorContainer}>
        <MaterialCommunityIcons name="alert-circle-outline" size={48} color="#FF3B30" />
        <Text style={styles.errorText}>Error: {error}</Text>
        <TouchableOpacity style={styles.retryButton} onPress={fetchForensicRequestDetails}>
          <Text style={styles.retryButtonText}>Retry</Text>
        </TouchableOpacity>
      </View>
    );
  }

  // Full-screen loader
  if (isLoading) {
    return (
      <View style={styles.fullScreenLoader}>
        <ActivityIndicator size="large" color="#0B36A1" />
      </View>
    );
  }

  // Data sections for the FlatList
  const sections = requestData ? [
    { type: 'caseDetails' },
    { type: 'evidences' }
  ] : [];

  return (
    <GestureHandlerRootView style={{ flex: 1 }}>
      <View style={styles.container}>
        <FlatList
          data={sections}
          renderItem={renderItem}
          keyExtractor={(item, index) => `${item.type}-${index}`}
          contentContainerStyle={{ flexGrow: 1 }}
          initialNumToRender={2}
          maxToRenderPerBatch={2}
          windowSize={3}
        />
        <Modal
          visible={showPreview}
          transparent={true}
          animationType="fade"
          onRequestClose={closePreview}
        >
          <PreviewComponent
            uri={previewUri}
            onClose={closePreview}
          />
        </Modal>

        <EvidenceAcceptanceModal
          visible={showEvidenceAcceptanceModal}
          status={requestData?.status}
          evidenceDetails={requestData?.evidence}
          onClose={handleEvidenceModalClose}
          onAccept={handleEvidenceAccept}
          onReject={handleEvidenceReject}
          forensicRequestId={forensicRequestId}
          token={token}
        />
      </View>
    </GestureHandlerRootView>
  );
};

export default React.memo(ForensicRequestDetails);

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: Colors.background,
  },
  detailsContainer: {
    paddingHorizontal: '4%',
    paddingVertical: 16,
  },
  detailRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    paddingVertical: 8,
    borderBottomWidth: 1,
    borderBottomColor: '#f0f0f0',
  },
  label: {
    fontSize: 14,
    color: Colors.lightText,
    width: '40%',
    fontFamily: 'Roboto_bold',
  },
  value: {
    fontSize: 14,
    color: Colors.black,
    width: '60%',
    fontFamily: 'Roboto',
  },
  sectionContainer: {
    marginTop: 12,
    marginBottom: 32,
    paddingHorizontal: '4%',
  },
  sectionTitle: {
    textAlign: 'left',
    color: Colors.primary,
    fontWeight: 'bold',
    fontFamily: 'Roboto_bold',
    fontSize: 20,
    marginBottom: 24,
    marginTop: 8,
  },

  errorContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 20,
    backgroundColor: Colors.background,
  },
  errorText: {
    color: Colors.error,
    marginVertical: 16,
    textAlign: 'center',
    fontSize: 16,
    fontFamily: 'Roboto',
  },
  retryButton: {
    backgroundColor: Colors.primary,
    paddingVertical: 10,
    paddingHorizontal: 20,
    borderRadius: 8,
  },
  retryButtonText: {
    color: Colors.background,
    fontSize: 16,
    fontFamily: 'Roboto_bold',
  },
  fullScreenLoader: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: Colors.background,
  },
  noEvidenceText: {
    textAlign: 'center',
    color: Colors.lightText,
    fontStyle: 'italic',
    marginTop: 20,
    marginBottom: 40,
    fontFamily: 'Roboto',
  },
});