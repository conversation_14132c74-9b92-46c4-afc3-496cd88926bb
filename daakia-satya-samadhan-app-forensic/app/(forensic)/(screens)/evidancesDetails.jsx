import {
    View,
    Text,
    TouchableOpacity,
    ScrollView,
    Image,
    StyleSheet,
    FlatList,
    useWindowDimensions,
    Modal,
    ActivityIndicator,
    Alert,
  } from 'react-native';
  import React, { useState, useEffect } from 'react';
  import VideoPlayer from '../../../components/Larges/VideoPlayer';
  import { router, useLocalSearchParams } from "expo-router";
  import { useAuth } from "../../../context/auth-context";
  import { transformUrl } from "../../../utils/transformUrl";
  import Constants from 'expo-constants';
import PreviewComponent from '../../../components/Larges/PreviewComponent';
import { Colors } from '../../../constants/colors';
import { getCurrentTime, timeUtils } from '../../../utils/currentTime';
import { apiService } from '../../../services/api';



export default function EvidenceDetails() {
  const { width } = useWindowDimensions();
  const [currentIndex, setCurrentIndex] = useState(0);
  const { evidenceId, caseId, forensicRequestId } = useLocalSearchParams();
  const [loading, setLoading] = useState(true);
  const [evidenceData, setEvidenceData] = useState(null);
  const [error, setError] = useState(null);
  const [selectedLab, setSelectedLab] = useState(null);
  const [sending, setSending] = useState(false);
  // Add states for preview functionality
  const [previewVisible, setPreviewVisible] = useState(false);
  const [previewUri, setPreviewUri] = useState('');
  
  const BASE_URL = Constants.expoConfig.extra.baseUrl;
  const { token, profile } = useAuth();
  
  const fetchEvidenceData = async () => {
    try {
      setLoading(true);
      
      const response = await apiService.fetchEvidenceDetails(token, caseId, evidenceId);
      
      if (response?.status === 'success' && response?.data) {
        const evidence = response.data;
        
        if (evidence) {
          if (evidence.attachmentUrl && Array.isArray(evidence.attachmentUrl) && evidence.attachmentUrl.length > 0) {
            evidence.attachmentUrl = evidence.attachmentUrl.map(url => 
              transformUrl(url)
            );
          } else {
            evidence.attachmentUrl = [];
          }
          
          if (!evidence.lab_department || !Array.isArray(evidence.lab_department)) {
            evidence.lab_department = [];
          }
          
          if (!evidence.tags || !Array.isArray(evidence.tags)) {
            evidence.tags = [];
          }
          
          setEvidenceData(evidence);
        } else {
          setError('Evidence data is empty');
        }
      } else {
        setError('Failed to fetch evidence data: Invalid response format');
      }
    } catch (err) {
      console.error('Error fetching evidence data:', err);
      setError('An error occurred while fetching evidence data');
    } finally {
      setLoading(false);
    }
  };
  
  useEffect(() => {
    fetchEvidenceData();
  }, [evidenceId, caseId, forensicRequestId, token, BASE_URL]);

  // Function to handle long press on media
  const handleLongPress = (uri) => {
    setPreviewUri(uri);
    setPreviewVisible(true);
  };

  // Function to close the preview
  const handleClosePreview = () => {
    setPreviewVisible(false);
  };



  const formatTagType = (type) => {
    return type.replace(/_/g, ' ').replace(/\b\w/g, l => l.toUpperCase());
  };

  // Function to get current and next lab/department based on user profile
  const getLabDepartmentFlow = () => {
    if (!evidenceData?.lab_department || !profile?.forensicProfile) {
      return { currentStep: null, nextStep: null, isCurrentUser: false };
    }

    const userLabId = profile.forensicProfile.lab._id;
    const userDeptId = profile.forensicProfile.labDepartment._id;

    // Sort lab_department by priority
    const sortedSteps = [...evidenceData.lab_department].sort((a, b) => a.priority - b.priority);

    // Find current user's step
    const currentUserStep = sortedSteps.find(step =>
      step.labId._id === userLabId && step.labDepartmentId._id === userDeptId
    );

    if (currentUserStep) {
      // Find next step based on priority
      const nextStep = sortedSteps.find(step => step.priority > currentUserStep.priority);
      return {
        currentStep: currentUserStep,
        nextStep: nextStep || null,
        isCurrentUser: true,
        allSteps: sortedSteps
      };
    }

    return {
      currentStep: null,
      nextStep: null,
      isCurrentUser: false,
      allSteps: sortedSteps
    };
  };

  const formatDateTime = (dateString) => {
    return new Date(dateString).toLocaleString('en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric',
      hour: 'numeric',
      minute: 'numeric',
      hour12: true
    });
  };

  const handleSendToNext = async () => {
    if (!selectedLab) {
      setError('Please select a lab and department first');
      return;
    }

    try {
      setSending(true);
      const response = await apiService.sendEvidenceToNextLab(token, caseId, evidenceId, selectedLab.labId._id, selectedLab.labDepartmentId._id);

      if (response?.status === 'success') {
        Alert.alert(
          'Success',
          `Evidence sent successfully to ${selectedLab.labId.name}, ${selectedLab.labDepartmentId.name}`,
          [
            {
              text: 'OK',
              onPress: async () => {
                setSelectedLab(null);
                await fetchEvidenceData();
              }
            }
          ]
        );
      } else {
        setError('Failed to send evidence to next lab');
      }
    } catch (err) {
      console.error('Error sending evidence:', err);
      Alert.alert('Error', err.message || 'Failed to send evidence. Please try again.');
    } finally {
      setSending(false);
    }
  };

  if (loading) {
    return (
      <View style={[styles.container, styles.centerContent]}>
        <ActivityIndicator size="large" color={Colors.primary} />
        <Text style={styles.loadingText}>Loading evidence details...</Text>
      </View>
    );
  }

  if (error || !evidenceData) {
    return (
      <View style={[styles.container, styles.centerContent]}>
        <Text style={styles.errorText}>{error || 'No evidence data available'}</Text>
      </View>
    );
  }
  
  return (
    <View style={styles.container}>
      <ScrollView>
        {/* Evidence Media Section */}
        <View style={styles.sectionContainer}>
          <Text style={styles.sectionLabel}>Evidence Media</Text>
          <Text style={styles.tooltipText}>Press and hold to preview</Text>
          <FlatList
            data={evidenceData.attachmentUrl}
            renderItem={({ item }) => (
              <TouchableOpacity
                activeOpacity={0.9}
                onLongPress={() => handleLongPress(item)}
                delayLongPress={200}
              >
                {item.endsWith('.mp4') ? (
                  <VideoPlayer 
                    uri={item} 
                    style={{ width: width - 32, height: width - 32 }} 
                  />
                ) : (
                  <Image
                    source={{ uri: item }}
                    style={[styles.evidenceMedia, { width: width - 32, height: width - 32 }]}
                    resizeMode="contain"
                  />
                )}
              </TouchableOpacity>
            )}
            keyExtractor={(_, index) => index.toString()}
            horizontal
            pagingEnabled
            showsHorizontalScrollIndicator={false}
            snapToInterval={width - 32}
            decelerationRate="fast"
            onScroll={(event) => {
              const offset = event.nativeEvent.contentOffset.x;
              const index = Math.round(offset / (width - 32));
              setCurrentIndex(index);
            }}
            contentContainerStyle={styles.mediaListContainer}
          />

          <View style={styles.swipeIndicatorContainer}>
            {evidenceData.attachmentUrl.map((_, index) => (
              <View
                key={index}
                style={[
                  styles.swipeIndicator,
                  index === currentIndex && styles.activeSwipeIndicator,
                ]}
              />
            ))}
          </View>
        </View>

        {/* Evidence Details Section */}
        <View style={styles.sectionContainer}>
          <Text style={styles.sectionLabel}>Evidence Information</Text>
          <View style={styles.detailsCard}>
            <View style={styles.detailRow}>
              <Text style={styles.detailLabel}>Type:</Text>
              <Text style={styles.detailValue}>{evidenceData.type}</Text>
            </View>

            <View style={styles.detailRow}>
              <Text style={styles.detailLabel}>Title:</Text>
              <Text style={styles.detailValue}>{evidenceData.title}</Text>
            </View>

            <View style={styles.detailRow}>
              <Text style={styles.detailLabel}>Description:</Text>
              <Text style={styles.detailValue}>{evidenceData.description}</Text>
            </View>

            {evidenceData.tags && evidenceData.tags.length > 0 && (
              <View style={styles.detailRow}>
                <Text style={styles.detailLabel}>Sample Type:</Text>
                <Text style={styles.detailValue}>
                  {evidenceData.tags.map((tag) => formatTagType(tag.type)).join(', ')}
                </Text>
              </View>
            )}

            <View style={styles.detailRow}>
              <Text style={styles.detailLabel}>Date & Time:</Text>
              <Text style={styles.detailValue}>{formatDateTime(evidenceData.time)}</Text>
            </View>

            <View style={styles.detailRow}>
              <Text style={styles.detailLabel}>Location:</Text>
              <Text style={styles.detailValue}>{evidenceData.gpsLocation}</Text>
            </View>

            {/* Lab and Department Info */}
            {(() => {
              const flowData = getLabDepartmentFlow();

              return (
                <>
                  {/* Current Lab and Department */}
                  {flowData.isCurrentUser && flowData.currentStep && (
                    <View style={styles.detailRow}>
                      <Text style={styles.detailLabel}>Current Lab:</Text>
                      <Text style={[styles.detailValue, styles.currentHighlight]}>
                        {flowData.currentStep.labId.name}, {flowData.currentStep.labDepartmentId.name}
                      </Text>
                    </View>
                  )}

                  {/* Send to Next Dropdown */}
                  {evidenceData.lab_department && evidenceData.lab_department.length > 0 && 
                   evidenceData.lab_department
                     .filter((step) => {
                       if (flowData.isCurrentUser && flowData.currentStep) {
                         return step._id !== flowData.currentStep._id;
                       }
                       return true;
                     }).length > 0 && (
                    <View style={styles.detailRow}>
                      <Text style={styles.detailLabel}>Send Next To:</Text>
                      <View style={styles.detailValue}>
                        <Text style={styles.dropdownLabel}>Select Lab & Department:</Text>
                        {evidenceData.lab_department
                          .sort((a, b) => a.priority - b.priority)
                          .filter((step) => {
                            if (flowData.isCurrentUser && flowData.currentStep) {
                              return step._id !== flowData.currentStep._id;
                            }
                            return true;
                          })
                          .map((step) => (
                            <TouchableOpacity
                              key={step._id}
                              style={[
                                styles.dropdownItem,
                                selectedLab?._id === step._id && styles.selectedDropdownItem
                              ]}
                              onPress={() => setSelectedLab(step)}
                            >
                              <Text style={[
                                styles.dropdownText,
                                selectedLab?._id === step._id && styles.selectedDropdownText
                              ]}>
                                {step.priority}. {step.labId.name}, {step.labDepartmentId.name}
                              </Text>
                            </TouchableOpacity>
                          ))}
                      </View>
                    </View>
                  )}

                  {!flowData.isCurrentUser && (
                    <View style={styles.detailRow}>
                      <Text style={styles.detailLabel}>Status:</Text>
                      <Text style={styles.detailValue}>Not assigned to your department</Text>
                    </View>
                  )}
                </>
              );
            })()}
          </View>
        </View>

        {/* Send Button Container - Now inside ScrollView */}
        <View style={styles.buttonContainer}>
          <TouchableOpacity
            style={[
              styles.submitButton,
              (!selectedLab || sending) && styles.disabledButton
            ]}
            onPress={handleSendToNext}
            disabled={!selectedLab || sending}
          >
            {sending ? (
              <ActivityIndicator size="small" color={Colors.background} />
            ) : (
              <Text style={styles.submitButtonText}>
                {selectedLab 
                  ? `SEND TO ${selectedLab.labId.name.toUpperCase()}, ${selectedLab.labDepartmentId.name.toUpperCase()}`
                  : 'SELECT LAB TO SEND'
                }
              </Text>
            )}
          </TouchableOpacity>
        </View>
      </ScrollView>

      {/* Preview Modal */}
      <Modal
        visible={previewVisible}
        transparent={true}
        animationType="fade"
        onRequestClose={handleClosePreview}
      >
        <PreviewComponent 
          uri={previewUri}
          onClose={handleClosePreview}
        />
      </Modal>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: Colors.background,
  },
  sectionContainer: {
    marginBottom: 20,
    paddingTop: 12,
  },
  mediaListContainer: {
    paddingHorizontal: 16,
  },
  sectionLabel: {
    fontSize: 20,
    fontWeight: 'bold',
    fontFamily: 'Roboto_bold',
    color: Colors.primary,
    marginBottom: 12,
    paddingHorizontal: 16,
  },
  evidenceMedia: {
    borderRadius: 12,
    marginRight: 16,

  },
  detailsCard: {
    backgroundColor: Colors.background,
    borderRadius: 12,
    padding: 16,
    marginHorizontal: 16,
    shadowColor: Colors.black,
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  detailRow: {
    flexDirection: 'row',
    paddingVertical: 10,
    borderBottomWidth: 0.5,
    borderBottomColor: Colors.border,
  },
  detailLabel: {
    width: '40%',
    fontSize: 14,
    fontWeight: '700',
    color: Colors.black,
    textAlign: 'left',
    fontFamily: 'Roboto_bold',
  },
  detailValue: {
    flex: 1,
    fontSize: 14,
    color: Colors.black,
  },
  swipeIndicatorContainer: {
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'center',
    marginTop: 12,
  },
  swipeIndicator: {
    width: 8,
    height: 8,
    borderRadius: 4,
    backgroundColor: Colors.lightText,
    marginHorizontal: 4,
  },
  activeSwipeIndicator: {
    backgroundColor: Colors.primary,
    width: 10,
    height: 10,
    borderRadius: 5,
  },
  tooltipText: {
    fontSize: 14,
    color: Colors.lightText,
    fontStyle: 'italic',
    marginBottom: 8,
    paddingHorizontal: 16,
    textAlign: 'center',
  },
  centerContent: {
    justifyContent: 'center',
    alignItems: 'center',
    paddingTop: 50,
  },
  errorText: {
    color: Colors.error,
    fontSize: 16,
    textAlign: 'center',
    fontFamily: 'Roboto',
  },
  tagContainer: {
    flex: 1,
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: 8,
  },
  tagItem: {
    backgroundColor: Colors.primary + '20',
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 16,
    marginRight: 8,
    marginBottom: 8,
  },
  tagText: {
    color: Colors.primary,
    fontSize: 12,
    fontFamily: 'Roboto_bold',
  },
  timeLeftText: {
    color: Colors.lightText,
    fontSize: 11,
    fontFamily: 'Roboto',
    marginTop: 2,
  },
  // Simple highlight style for current lab/department
  currentHighlight: {
    color: Colors.primary,
    fontFamily: 'Roboto_bold',
  },
  // Dropdown styles
  dropdownLabel: {
    fontSize: 12,
    color: Colors.lightText,
    marginBottom: 8,
    fontFamily: 'Roboto',
  },
  dropdownItem: {
    backgroundColor: Colors.background,
    borderWidth: 1,
    borderColor: Colors.border,
    borderRadius: 6,
    padding: 12,
    marginBottom: 6,
    elevation: 1,
    shadowColor: Colors.black,
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
  },
  dropdownText: {
    fontSize: 14,
    color: Colors.black,
    fontFamily: 'Roboto',
  },
  loadingText: {
    marginTop: 12,
    fontSize: 16,
    color: Colors.primary,
    fontFamily: 'Roboto',
  },
  selectedDropdownItem: {
    backgroundColor: '#E8E8E8',
    borderColor: '#0B36A1',
    borderWidth: 1,
  },
  selectedDropdownText: {
    color: '#0B36A1',
    fontFamily: 'Roboto_bold',
  },
  buttonContainer: {

    justifyContent: 'space-between',
    marginHorizontal: '2%',
    marginTop: 16,
    marginBottom: 24,
    paddingHorizontal: 16,
  },
  submitButton: {
    backgroundColor: Colors.primary,
    borderRadius: 50,
    paddingVertical: 12,
    alignItems: 'center',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
  },
  disabledButton: {
    backgroundColor: Colors.disabled,
    fontFamily: 'Roboto',
    fontWeight: 'bold',
  },
  submitButtonText: {
    color: Colors.background,
    fontSize: 16,
    fontWeight: 'bold',
    fontFamily: 'Roboto',
  },
});