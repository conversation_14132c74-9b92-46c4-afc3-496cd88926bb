import React from 'react';
import { View, Text, TouchableOpacity, Image, StyleSheet } from 'react-native';
import MaterialCommunityIcons from "@expo/vector-icons/MaterialCommunityIcons";
import { Colors } from "../constants/colors";
import { transformUrl } from "../utils/transformUrl";

const EvidenceCard = ({ 
  evidence, 
  onPress, 
  onSharePress 
}) => {
  const renderEvidenceMedia = (url) => {
    if (!url) return <View style={styles.evidenceImage} />;

    const transformedUrl = transformUrl(url);
    const isVideo = /\.(mp4|mov|avi|wmv|flv|webm|mkv)$/i.test(url);

    if (isVideo) {
      return (
        <View style={styles.evidenceImage}>
          <MaterialCommunityIcons name="play-circle" size={30} color="#FFFFFF" style={styles.playIcon} />
          <View style={styles.videoOverlay}>
            <Text style={styles.videoLabel}>VIDEO</Text>
          </View>
        </View>
      );
    } else {
      return (
        <Image
          source={{ uri: transformedUrl }}
          style={styles.evidenceImage}
          defaultSource={require('../assets/images/small_satya_smadhanLogo.png')}
        />
      );
    }
  };

  return (
    <TouchableOpacity
      style={styles.evidenceCard}
      onPress={onPress}
    >
      <View style={styles.imageContainer}>
        {renderEvidenceMedia(evidence.attachmentUrl?.[0])}
      </View>
      <View style={styles.evidenceDetails}>
        <Text style={styles.evidenceTitle}>{evidence.title || 'Untitled Evidence'}</Text>

        <View style={styles.detailRow}>
          <Text style={styles.evidenceLabel}>Type</Text>
          <Text style={styles.evidenceType}>{evidence.type || 'Unknown'}</Text>
        </View>

        {evidence.description && (
          <View style={styles.detailRow}>
            <Text style={styles.evidenceLabel}>Description:</Text>
            <Text style={styles.evidenceType}>{evidence.description}</Text>
          </View>
        )}

        {evidence.attachmentUrl && evidence.attachmentUrl.length > 1 && (
          <View style={styles.detailRow}>
            <Text style={styles.evidenceLabel}>Attachments:</Text>
            <Text style={styles.evidenceType}>{evidence.attachmentUrl.length}</Text>
          </View>
        )}
      </View>
      <TouchableOpacity
        style={styles.shareButton}
        onPress={onSharePress}
      >
        <MaterialCommunityIcons
          name="share-circle"
          size={24}
          color="#0B36A1"
        />
      </TouchableOpacity>
    </TouchableOpacity>
  );
};

const styles = StyleSheet.create({
  evidenceCard: {
    borderColor: Colors.border,
    borderWidth: 1,
    marginBottom: 16,
    padding: 12,
    borderRadius: 12,
    flexDirection: 'row',
    position: 'relative',
  },
  imageContainer: {
    width: 80,
    marginRight: 12,
    justifyContent: 'center',
    alignItems: 'center',
  },
  evidenceImage: {
    height: 80,
    width: 80,
    borderRadius: 8,
    backgroundColor: '#f0f0f0',
  },
  evidenceDetails: {
    flex: 1,
    gap: 6,
  },
  detailRow: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 4,
  },
  evidenceLabel: {
    fontSize: 12,
    color: Colors.lightText,
    width: 80,
    fontFamily: 'Roboto',
  },
  evidenceType: {
    fontSize: 12,
    color: Colors.lightText,
    flex: 1,
    fontFamily: 'Roboto',
  },
  evidenceTitle: {
    fontSize: 15,
    color: Colors.black,
    fontFamily: 'Roboto_bold',
    marginBottom: 8,
  },
  shareButton: {
    padding: 4,
    justifyContent: 'center',
    alignItems: 'center',
  },
  videoOverlay: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    backgroundColor: 'rgba(0, 0, 0, 0.4)',
    justifyContent: 'center',
    alignItems: 'center',
    borderRadius: 8,
  },
  videoLabel: {
    color: '#FFFFFF',
    fontWeight: 'bold',
    fontSize: 12,
    backgroundColor: 'rgba(0, 0, 0, 0.6)',
    paddingHorizontal: 6,
    paddingVertical: 3,
    borderRadius: 4,
    fontFamily: 'Roboto',
  },
  playIcon: {
    position: 'absolute',
    top: '50%',
    left: '50%',
    marginLeft: -15,
    marginTop: -15,
    zIndex: 10,
  },
});

export default EvidenceCard; 