import React, { useState } from 'react';
import {
  View,
  Text,
  Modal,
  TouchableOpacity,
  TextInput,
  StyleSheet,
  ActivityIndicator,
  Alert
} from 'react-native';
import MaterialCommunityIcons from '@expo/vector-icons/MaterialCommunityIcons';
import Constants from 'expo-constants';
import { Colors } from '../../constants/colors';
import SuccessScreen from '../Smalls/SuccessScreen';
import { apiService } from '../../services/api';

const BASE_URL = Constants.expoConfig.extra.baseUrl;

/**
 * EvidenceAcceptanceModal - Modal for accepting or rejecting forensic evidence
 *
 * This modal is shown when the forensic request status is one of:
 * - "dispatched" - Evidence has been dispatched to the lab
 * - "rejected" - Evidence was previously rejected and can be re-evaluated
 * - "initiated" - Forensic request has been initiated
 *
 * The modal allows lab personnel to:
 * - Accept evidence (changes status to "received")
 * - Reject evidence with a mandatory reason (changes status to "rejected")
 */
const EvidenceAcceptanceModal = ({ visible, onClose, onAccept, onReject, forensicRequestId, token, status, evidenceDetails }) => {
  const [rejectionReason, setRejectionReason] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const [showSuccessScreen, setShowSuccessScreen] = useState(false);
  const [successMessage, setSuccessMessage] = useState('');

  // Define which statuses should show the modal
  const MODAL_VISIBLE_STATUSES = ['dispatched', 'rejected', 'initiated'];

  // Check if modal should be visible based on status
  const shouldShowModal = visible && status && MODAL_VISIBLE_STATUSES.includes(status.toLowerCase());

  // API call to update forensic request status
  const updateForensicRequestStatus = async (status, comment = '') => {
    try {
      setIsLoading(true);

      const result = await apiService.updateForensicRequest(token, forensicRequestId, status, comment);

      if (result.status === 'success') {
        // Show success animation
        setSuccessMessage(`Evidence ${status === 'received' ? 'accepted' : 'rejected'} successfully`);
        setShowSuccessScreen(true);

        // Call the appropriate callback after a short delay
        setTimeout(() => {
          setRejectionReason('');
          if (status === 'received' && onAccept) {
            onAccept(result.data);
          } else if (status === 'rejected' && onReject) {
            onReject(result.data);
          }
        }, 100);
      } else {
        Alert.alert('Error', result.message || 'Failed to update evidence status');
      }
    } catch (error) {
      Alert.alert('Error', 'Network error: ' + error.message);
    } finally {
      setIsLoading(false);
    }
  };

  const handleAccept = () => {
    updateForensicRequestStatus('received');
  };

  const handleReject = () => {
    if (!rejectionReason.trim()) {
      Alert.alert('Error', 'Please provide a reason for rejection');
      return;
    }
    updateForensicRequestStatus('rejected', rejectionReason.trim());
  };

  // Handle success animation completion
  const handleSuccessComplete = () => {
    setShowSuccessScreen(false);
    onClose(); // This will navigate back to caseDetails page
  };

  return (
    <Modal
      visible={shouldShowModal}
      transparent={true}
      animationType="fade"
      onRequestClose={onClose}
    >
      <View style={styles.modalOverlay}>
        <View style={styles.accessRequestContainer}>
          <View style={styles.modalHeader}>
            <Text style={styles.modalTitle}>Evidence Detail</Text>
            <TouchableOpacity 
              style={{padding: 4}}
              onPress={onClose}
            >
              <MaterialCommunityIcons name="close-circle" size={24} color={Colors.primary} />
            </TouchableOpacity>
          </View>
          
          <View style={styles.caseAcceptanceContent}>
            {status && status.toLowerCase() === 'rejected' ? (
              <>
                {/* <MaterialCommunityIcons
                  name="alert-circle-outline"
                  size={40}
                  color={Colors.error}
                  style={{alignSelf: 'center', marginBottom: 12}}
                /> */}
                <View style={styles.rejectedMessageContainer}>
                  <Text style={styles.rejectedMessageTitle}>Evidence Rejected</Text>
                  <Text style={styles.rejectedMessageText}>
                    This evidence was previously rejected. You can review and re-evaluate it below.
                  </Text>
                </View>
              </>
            ) : (
              <>
                {/* <MaterialCommunityIcons
                  name="clipboard-check-outline"
                  size={40}
                  color={Colors.primary}
                  style={{alignSelf: 'center', marginBottom: 12}}
                /> */}
                <Text style={styles.caseAcceptanceText}>
                  Before creating a report for this evidence, please confirm if you accept it as valid evidence.
                </Text>
              </>
            )}

            {/* Evidence Details Section */}
            {evidenceDetails && (
              <View style={styles.evidenceDetailsContainer}>
                <Text style={styles.evidenceDetailsTitle}>Package Details</Text>

                <View style={styles.evidenceDetailRow}>
                  <Text style={styles.evidenceDetailLabel}>Title:</Text>
                  <Text style={styles.evidenceDetailValue}>{evidenceDetails.title || 'N/A'}</Text>
                </View>

                <View style={styles.evidenceDetailRow}>
                  <Text style={styles.evidenceDetailLabel}>Type:</Text>
                  <Text style={styles.evidenceDetailValue}>{evidenceDetails.type || 'N/A'}</Text>
                </View>

                <View style={styles.evidenceDetailRow}>
                  <Text style={styles.evidenceDetailLabel}>Description:</Text>
                  <Text style={styles.evidenceDetailValue}>{evidenceDetails.description || 'N/A'}</Text>
                </View>

                <View style={styles.evidenceDetailRow}>
                  <Text style={styles.evidenceDetailLabel}>Created By:</Text>
                  <Text style={styles.evidenceDetailValue}>{evidenceDetails.createdBy?.name || 'N/A'}</Text>
                </View>

                <View style={styles.evidenceDetailRow}>
                  <Text style={styles.evidenceDetailLabel}>Created At:</Text>
                  <Text style={styles.evidenceDetailValue}>
                    {evidenceDetails.createdAt ? new Date(evidenceDetails.createdAt).toLocaleString() : 'N/A'}
                  </Text>
                </View>

                {evidenceDetails.attachmentUrl && evidenceDetails.attachmentUrl.length > 0 && (
                  <View style={styles.evidenceDetailRow}>
                    <Text style={styles.evidenceDetailLabel}>Attachments:</Text>
                    <Text style={styles.evidenceDetailValue}>{evidenceDetails.attachmentUrl.length} file(s)</Text>
                  </View>
                )}
              </View>
            )}
            
            <View style={styles.rejectionContainer}>
              <Text style={styles.rejectionLabel}>
                Reason for Rejection (required if rejecting):
              </Text>
              <TextInput 
                style={styles.rejectionInput}
                value={rejectionReason}
                onChangeText={setRejectionReason}
                placeholder="Enter reason for rejection"
                multiline={true}
                numberOfLines={3}
              />
            </View>
            
            <View style={styles.caseButtonContainer}>
              <TouchableOpacity
                style={[styles.actionButton, styles.rejectButton]}
                onPress={handleReject}
                disabled={isLoading}
              >
                {isLoading ? (
                  <ActivityIndicator size="small" color={Colors.background} />
                ) : (
                  <>
                    <MaterialCommunityIcons name="close-circle" size={14} color={Colors.background} />
                    <Text style={styles.buttonText}>Reject Evidence</Text>
                  </>
                )}
              </TouchableOpacity>
              <View style={{width: 10}} />
              <TouchableOpacity
                style={[styles.actionButton, styles.approveButton]}
                onPress={handleAccept}
                disabled={isLoading}
              >
                {isLoading ? (
                  <ActivityIndicator size="small" color={Colors.background} />
                ) : (
                  <>
                    <MaterialCommunityIcons name="check-circle" size={14} color={Colors.background} />
                    <Text style={styles.buttonText}>Accept & Report</Text>
                  </>
                )}
              </TouchableOpacity>
            </View>
          </View>
        </View>
      </View>

      {/* Success Screen Overlay */}
      {showSuccessScreen && (
        <SuccessScreen
          message={successMessage}
          duration={2500}
          onComplete={handleSuccessComplete}
        />
      )}
    </Modal>
  );
};

const styles = StyleSheet.create({
  modalOverlay: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
  },
  accessRequestContainer: {
    width: '90%',
    backgroundColor: Colors.background,
    borderRadius: 10,
    padding: 20,
    elevation: 5,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.25,
    shadowRadius: 3.84,
  },
  modalHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 20,
  },
  modalTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: Colors.black,
  },
  caseAcceptanceContent: {
    alignItems: 'stretch',
  },
  caseAcceptanceText: {
    fontSize: 14,
    color: Colors.black,
    textAlign: 'left',
    marginBottom: 20,
    lineHeight: 20,
  },
  rejectionContainer: {
    marginBottom: 20,
  },
  rejectionLabel: {
    fontSize: 14,
    fontWeight: '500',
    color: Colors.black,
    marginBottom: 8,
  },
  rejectionInput: {
    borderWidth: 1,
    borderColor: Colors.border,
    borderRadius: 8,
    padding: 12,
    fontSize: 14,
    backgroundColor: '#F8F9FA',
    textAlignVertical: 'top',
    minHeight: 80,
  },
  caseButtonContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  actionButton: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 12,
    paddingHorizontal: 16,
    borderRadius: 30,
    gap: 6,
  },
  rejectButton: {
    backgroundColor: Colors.error,
  },
  approveButton: {
    backgroundColor: Colors.primary,
  },
  buttonText: {
    color: Colors.background,
    fontSize: 14,
    fontWeight: '600',
  },
  rejectedMessageContainer: {
    backgroundColor: '#FFF5F5',
    borderWidth: 1,
    borderColor: '#FEB2B2',
    borderRadius: 20,
    padding: 16,
    marginBottom: 20,
  },
  rejectedMessageTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: Colors.error,
    marginBottom: 8,
    textAlign: 'center',
  },
  rejectedMessageText: {
    fontSize: 14,
    color: '#C53030',
    textAlign: 'left',
    lineHeight: 20,
  },
  evidenceDetailsContainer: {
    marginBottom: 20,
  },
  evidenceDetailsTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: Colors.black,
    marginBottom: 12,
    textAlign: 'left',
  },
  evidenceDetailRow: {
    flexDirection: 'row',
    marginBottom: 8,
    alignItems: 'flex-start',
  },
  evidenceDetailLabel: {
    fontSize: 14,
    fontWeight: '500',
    color: Colors.lightText,
    width: '30%',
    textAlign: 'left',
  },
  evidenceDetailValue: {
    fontSize: 14,
    color: Colors.black,
    flex: 1,
    textAlign: 'left',
    lineHeight: 20,
  },
});

export default EvidenceAcceptanceModal;
