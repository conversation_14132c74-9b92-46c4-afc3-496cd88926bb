import { View, StyleSheet, useWindowDimensions } from 'react-native';
import { Colors } from '../../constants/colors';



const RecentCasesSkeleton = () => {
  const { width, height } = useWindowDimensions();

  return (
    <View style={styles.container}>
      {/* Search Bar */}
      <View style={[styles.searchContainer, { marginBottom: height * 0.025 }]}>
        <View style={styles.searchRow}>
          <View style={[styles.searchInputWrapper, { height: 48, flex: 1 }]}>
            <View style={styles.searchIcon} />
            <View style={styles.searchInput} />
          </View>
          <View style={[styles.filterSelector, { flexDirection: 'row', alignItems: 'center', paddingHorizontal: 12 }]}>
            <View style={styles.filterText} />
            <View style={styles.filterIcon} />
          </View>
        </View>
      </View>

      {/* Active Filter Skeleton */}
      <View style={styles.activeFilterRow}>
        <View style={styles.activeFilterLabel} />
        <View style={styles.activeFilterChip} />
      </View>

      {/* Column Headers */}
      <View style={[styles.columnHeaderContainer, { marginBottom: height * 0.02 }]}>
        <View style={styles.caseNameHeader} />
        <View style={styles.columnTimeContainer}>
          <View style={styles.timeHeader} />
          <View style={styles.dateHeader} />
        </View>
      </View>

      {/* List Items */}
      <View style={styles.skeletonContainer}>
        {[1, 2, 3, 4, 5].map((item) => (
          <View key={item} style={styles.caseListItem}>
            <View style={styles.caseNameContainer}>
              <View style={styles.folderIcon} />
              <View style={styles.textContainer}>
                <View style={styles.caseTitle} />
                <View style={styles.caseSubtitle} />
              </View>
            </View>
            <View style={styles.timeContainer}>
              <View style={styles.timeText} />
              <View style={styles.dateText} />
            </View>
          </View>
        ))}
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: Colors.background,
    paddingTop: 20,
  },
  searchContainer: {
    marginHorizontal: '5%',
  },
  searchRow: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 12,
  },
  filterSelector: {
    width: 90,
    height: 48,
    backgroundColor: '#F5F5F5',
    borderRadius: 8,
  },
  filterText: {
    width: 40,
    height: 16,
    backgroundColor: '#E0E0E0',
    borderRadius: 4,
    marginRight: 8,
  },
  filterIcon: {
    width: 16,
    height: 16,
    backgroundColor: '#E0E0E0',
    borderRadius: 4,
  },
  searchInputWrapper: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#F5F5F5',
    borderRadius: 8,
    paddingHorizontal: 12,
  },
  searchIcon: {
    width: 20,
    height: 20,
    backgroundColor: '#E0E0E0',
    borderRadius: 4,
  },
  searchInput: {
    flex: 1,
    height: 20,
    backgroundColor: '#E0E0E0',
    borderRadius: 4,
    marginLeft: 12,
  },
  columnHeaderContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginHorizontal: '5%',
  },
  caseNameHeader: {
    width: 100,
    height: 16,
    backgroundColor: '#E0E0E0',
    borderRadius: 4,
  },
  columnTimeContainer: {
    flexDirection: 'row',
    width: '40%',
    justifyContent: 'space-around',
  },
  timeHeader: {
    width: 60,
    height: 16,
    backgroundColor: '#E0E0E0',
    borderRadius: 4,
  },
  dateHeader: {
    width: 60,
    height: 16,
    backgroundColor: '#E0E0E0',
    borderRadius: 4,
  },
  skeletonContainer: {
    flex: 1,
    paddingHorizontal: '5%',
  },
  caseListItem: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    paddingVertical: 20,
    borderTopWidth: 1,
    borderColor: Colors.border,
    alignItems: 'flex-start',
  },
  caseNameContainer: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'flex-start',
    marginRight: 15,
  },
  folderIcon: {
    width: 24,
    height: 24,
    backgroundColor: '#E0E0E0',
    borderRadius: 12,
  },
  textContainer: {
    flex: 1,
    marginLeft: 12,
  },
  caseTitle: {
    width: '80%',
    height: 20,
    backgroundColor: '#E0E0E0',
    borderRadius: 4,
    marginBottom: 10,
  },
  caseSubtitle: {
    width: '60%',
    height: 16,
    backgroundColor: '#E0E0E0',
    borderRadius: 4,
  },
  timeContainer: {
    width: '35%',
    alignItems: 'flex-end',
  },
  timeText: {
    width: 40,
    height: 16,
    backgroundColor: '#E0E0E0',
    borderRadius: 4,
    marginBottom: 10,
  },
  dateText: {
    width: 60,
    height: 16,
    backgroundColor: '#E0E0E0',
    borderRadius: 4,
  },
  activeFilterRow: {
    flexDirection: 'row',
    alignItems: 'center',
    marginLeft: '5%',
    marginBottom: 16,
    gap: 12,
  },
  activeFilterLabel: {
    width: 80,
    height: 18,
    backgroundColor: '#E0E0E0',
    borderRadius: 4,
  },
  activeFilterChip: {
    width: 90,
    height: 32,
    backgroundColor: '#E0E0E0',
    borderRadius: 16,
  },
});

export default RecentCasesSkeleton; 