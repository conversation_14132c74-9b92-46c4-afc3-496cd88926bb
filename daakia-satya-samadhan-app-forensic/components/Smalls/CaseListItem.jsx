import { View, Text, TouchableOpacity, StyleSheet } from 'react-native';
import FontAwesome from "@expo/vector-icons/FontAwesome";
import { Colors } from '../../constants/colors';

const createStyles = (width) => StyleSheet.create({
  caseListItem: {
    flexDirection: "row",
    justifyContent: "space-between",
    paddingHorizontal: width * 0.05,
    paddingVertical: 15,
    borderTopWidth: 1,
    borderColor: Colors.border,
    alignItems: "flex-start",
  },
  caseNameContainer: {
    flex: 1,
    flexDirection: "row",
    alignItems: "flex-start",
    gap: 10,
    marginRight: 10,
  },
  caseName: {
    fontSize: 16,
    color: Colors.black,
    flex: 1,
  },
  caseSubText: {
    fontSize: 12,
    opacity: 0.6,
    color: Colors.black,
    flex: 1,
  },
  timeContainer: {
    width: width * 0.35,
    justifyContent: "space-between",
    alignItems: "flex-end",
  },
  timeText: {
    width: width * 0.15,
    textAlign: "right",
    color: Colors.black,
  },
});

const CaseListItem = ({ item, width, onPress }) => {
  const styles = createStyles(width);

  return (
    <TouchableOpacity
      style={styles.caseListItem}
      onPress={onPress}
    >
      <View style={styles.caseNameContainer}>
        <FontAwesome name="folder" size={24} color="#0B36A1" />
        <View style={{ flex: 1 }}>
          <Text style={styles.caseName} numberOfLines={1} ellipsizeMode="tail">
            {item.CaseName}
          </Text>
          <Text style={styles.caseSubText} numberOfLines={2} ellipsizeMode="tail">
            {item.labName}, {item.labdepartmentId?.name || 'Unknown Department'}
          </Text>
        </View>
      </View>
      <View style={styles.timeContainer}>
        <Text style={styles.timeText}>
          {item.time}
        </Text>
        <Text style={{ textAlign: 'right' }}>{item.date}</Text>
      </View>
    </TouchableOpacity>
  );
};

export default CaseListItem; 