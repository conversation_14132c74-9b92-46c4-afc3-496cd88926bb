import { View, Text, TouchableOpacity, StyleSheet } from 'react-native';
import FontAwesome from "@expo/vector-icons/FontAwesome";
import { Colors } from '../../constants/colors';

const createStyles = () => StyleSheet.create({
  errorContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: Colors.background,
    padding: 20,
  },
  errorText: {
    color: 'red',
    textAlign: 'center',
    marginBottom: 20,
  },
  retryButton: {
    backgroundColor: Colors.primary,
    padding: 10,
    borderRadius: 5,
  },
  retryButtonText: {
    color: Colors.background,
  },
});

const ErrorView = ({ error, onRetry }) => {
  const styles = createStyles();

  return (
    <View style={styles.errorContainer}>
      <FontAwesome name="exclamation-circle" size={48} color="red" style={{ marginBottom: 16 }} />
      <Text style={styles.errorText}>{error}</Text>
      <TouchableOpacity 
        onPress={onRetry}
        style={styles.retryButton}
      >
        <Text style={styles.retryButtonText}>Retry</Text>
      </TouchableOpacity>
    </View>
  );
};

export default ErrorView; 