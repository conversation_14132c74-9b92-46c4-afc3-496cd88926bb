import { View, Text, TouchableOpacity, TextInput, StyleSheet } from 'react-native';
import FontAwesome from "@expo/vector-icons/FontAwesome";
import { Colors } from '../../constants/colors';

const createStyles = (width, height) => StyleSheet.create({
  searchContainer: {
    marginHorizontal: width * 0.05,
    marginTop: height * 0.02,
    marginBottom: height * 0.01,
  },
  searchInputWrapper: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#F5F5F5',
    borderRadius: 8,
    paddingHorizontal: 12,
  },
  searchInput: {
    flex: 1,
    paddingVertical: 12,
    paddingHorizontal: 8,
    fontSize: 16,
  },
  filterButton: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 12,
    borderRadius: 8,
    gap: 6,
  },
  filterButtonActive: {
    backgroundColor: Colors.primary,
  },
  filterButtonInactive: {
    backgroundColor: '#F5F5F5',
  },
  filterText: {
    fontSize: 16,
  },
  filterTextActive: {
    color: '#FFFFFF',
  },
  filterTextInactive: {
    color: Colors.lightText,
  },
  rowContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 12,
  },
});

const SearchBar = ({ width, height, searchQuery, setSearchQuery, showFilters, setShowFilters }) => {
  const styles = createStyles(width, height);

  return (
    <View style={styles.searchContainer}>
      <View style={styles.rowContainer}>
        <View style={styles.searchInputWrapper}>
          <FontAwesome name="search" size={20} color={Colors.lightText} />
          <TextInput
            style={styles.searchInput}
            placeholder="Search cases..."
            value={searchQuery}
            onChangeText={setSearchQuery}
          />
          {searchQuery !== '' && (
            <TouchableOpacity onPress={() => setSearchQuery('')}>
              <FontAwesome name="times-circle" size={20} color={Colors.lightText} />
            </TouchableOpacity>
          )}
        </View>

        <TouchableOpacity
          onPress={() => setShowFilters(!showFilters)}
          style={[
            styles.filterButton,
            showFilters ? styles.filterButtonActive : styles.filterButtonInactive
          ]}
        >
          <Text style={[
            styles.filterText,
            showFilters ? styles.filterTextActive : styles.filterTextInactive
          ]}>
            Filter
          </Text>
          <FontAwesome
            name="filter"
            size={20}
            color={showFilters ? '#FFFFFF' : Colors.lightText}
          />
        </TouchableOpacity>
      </View>
    </View>
  );
};

export default SearchBar; 