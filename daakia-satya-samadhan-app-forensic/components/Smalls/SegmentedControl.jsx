import React from 'react';
import { View, Text, TouchableOpacity, StyleSheet } from 'react-native';
import MaterialCommunityIcons from '@expo/vector-icons/MaterialCommunityIcons';
import { Colors } from '../../constants/colors';

const SegmentedControl = ({ 
  segments, 
  activeSegment, 
  onSegmentPress,
  containerStyle,
  segmentStyle,
  activeSegmentStyle,
  textStyle,
  activeTextStyle
}) => {
  return (
    <View style={[styles.segmentedControlContainer, containerStyle]}>
      <View style={[styles.segmentedControl, segmentStyle]}>
        {segments.map((segment, index) => (
          <TouchableOpacity
            key={segment.key || index}
            style={[
              styles.segmentButton, 
              activeSegment === segment.key && styles.activeSegment,
              activeSegment === segment.key && activeSegmentStyle
            ]}
            onPress={() => onSegmentPress(segment.key)}
          >
            {segment.icon && (
              <MaterialCommunityIcons 
                name={segment.icon} 
                size={18} 
                color={activeSegment === segment.key ? Colors.background : Colors.lightText} 
              />
            )}
            <Text 
              style={[
                activeSegment === segment.key ? styles.activeSegmentText : styles.segmentText,
                activeSegment === segment.key ? activeTextStyle : textStyle
              ]}
            >
              {segment.title}
            </Text>
          </TouchableOpacity>
        ))}
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  segmentedControlContainer: {
    alignItems: 'flex-start',
    marginBottom: 24,
  },
  segmentedControl: {
    flexDirection: 'row',
    borderRadius: 30,
    overflow: 'hidden',
    borderWidth: 1,
    borderColor: Colors.border,
    backgroundColor: '#E8E8E8',
  },
  segmentButton: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 10,
    paddingHorizontal: 20,
    justifyContent: 'center',
    width: 150,
  },
  activeSegment: {
    backgroundColor: '#367E18',
    borderRadius: 30,
  },
  segmentText: {
    color: '#666666',
    marginLeft: 8,
    fontWeight: '500',
    fontFamily: 'Roboto',
  },
  activeSegmentText: {
    color: '#FFFFFF',
    marginLeft: 8,
    fontWeight: '500',
    fontFamily: 'Roboto',
  },
});

export default SegmentedControl;
