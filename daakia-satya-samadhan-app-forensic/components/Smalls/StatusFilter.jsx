import { View, Text, TouchableOpacity, StyleSheet } from 'react-native';
import { RECENT_CASES_STATUS, RECENT_CASES_STATUS_DISPLAY_NAMES } from '../../services/validation';
import { Colors } from '../../constants/colors';

const createStyles = (width, height) => StyleSheet.create({
  container: {
    marginHorizontal: width * 0.05,
    marginTop: height * 0.01,
    marginBottom: height * 0.01,
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: 10,
  },
  filterButton: {
    paddingHorizontal: 12,
    paddingVertical: 8,
    borderRadius: 8,
    flexDirection: 'row',
    alignItems: 'center',
    gap: 5,
  },
  filterButtonActive: {
    backgroundColor: Colors.primary,
  },
  filterButtonInactive: {
    backgroundColor: '#F5F5F5',
  },
  filterText: {
    color: Colors.lightText,
  },
  filterTextActive: {
    color: '#FFFFFF',
  },
});

const StatusFilter = ({ width, height, selectedStatus, setSelectedStatus, visible }) => {
  const styles = createStyles(width, height);

  if (!visible) return null;

  return (
    <View style={styles.container}>
      {Object.entries(RECENT_CASES_STATUS).map(([key, value]) => (
        <TouchableOpacity
          key={key}
          style={[
            styles.filterButton,
            selectedStatus === value ? styles.filterButtonActive : styles.filterButtonInactive
          ]}
          onPress={() => setSelectedStatus(value)}
        >
          <Text style={[
            styles.filterText,
            selectedStatus === value && styles.filterTextActive
          ]}>
            {RECENT_CASES_STATUS_DISPLAY_NAMES[value]}
          </Text>
        </TouchableOpacity>
      ))}
    </View>
  );
};

export default StatusFilter; 