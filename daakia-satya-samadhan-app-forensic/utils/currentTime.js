import moment from 'moment';

/**
 * Get current time in various formats using moment.js
 */
export const getCurrentTime = {
  // Get current time in default format (ISO string)
  now: () => moment().toISOString(),

  // Get current time in custom format
  format: (format = 'YYYY-MM-DD HH:mm:ss') => moment().format(format),

  // Get current date only
  date: () => moment().format('YYYY-MM-DD'),

  // Get current time only (24-hour format)
  time: () => moment().format('HH:mm:ss'),

  // Get current time only (12-hour format with AM/PM)
  time12: () => moment().format('hh:mm:ss A'),

  // Get current timestamp (Unix timestamp in seconds)
  timestamp: () => moment().unix(),

  // Get current timestamp in milliseconds
  timestampMs: () => moment().valueOf(),

  // Get current time in specific timezone
  timezone: (timezone) => moment().tz(timezone).format('YYYY-MM-DD HH:mm:ss'),

  // Get current time for display (human readable)
  display: () => moment().format('MMM DD, YYYY hh:mm A'),

  // Get current time for file naming (no special characters)
  filename: () => moment().format('YYYYMMDD_HHmmss'),

  // Get relative time from now (e.g., "2 hours ago", "in 3 days")
  fromNow: (date) => moment(date).fromNow(),

  // Get current moment object for advanced operations
  moment: () => moment(),

  // Common Indian time format
  indian: () => moment().format('DD/MM/YYYY hh:mm A'),

  // ISO date format
  iso: () => moment().toISOString(),

  // Get current day name
  dayName: () => moment().format('dddd'),

  // Get current month name
  monthName: () => moment().format('MMMM'),

  // Get current year
  year: () => moment().format('YYYY')
};

/**
 * Utility functions for time operations
 */
export const timeUtils = {
  // Check if a date is today
  isToday: (date) => moment(date).isSame(moment(), 'day'),

  // Check if a date is yesterday
  isYesterday: (date) => moment(date).isSame(moment().subtract(1, 'day'), 'day'),

  // Check if a date is tomorrow
  isTomorrow: (date) => moment(date).isSame(moment().add(1, 'day'), 'day'),

  // Get difference between two dates
  diff: (date1, date2, unit = 'days') => moment(date1).diff(moment(date2), unit),

  // Add time to current date
  addTime: (amount, unit = 'days') => moment().add(unit, amount).toISOString(),

  // Subtract time from current date
  subtractTime: (amount, unit = 'days') => moment().subtract(unit, amount).toISOString(),

  // Format any date
  formatDate: (date, format = 'YYYY-MM-DD HH:mm:ss') => moment(date).format(format),

  // Check if date is valid
  isValid: (date) => moment(date).isValid(),

  // Get start of day
  startOfDay: (date = moment()) => moment(date).startOf('day').toISOString(),

  // Get end of day
  endOfDay: (date = moment()) => moment(date).endOf('day').toISOString(),

  // Format date in GB format (DD/MM/YYYY)
  formatDateGB: (dateString) => {
    try {
      const date = moment(dateString);
      if (!date.isValid()) throw new Error('Invalid date');
      return date.format('DD/MM/YYYY');
    } catch (e) {
      console.error('Date formatting error:', e);
      return 'Invalid Date';
    }
  },

  // Format time in 24-hour format (HH:mm)
  formatTime24: (timeString) => {
    try {
      const date = moment(timeString);
      if (!date.isValid()) throw new Error('Invalid time');
      return date.format('HH:mm');
    } catch (e) {
      console.error('Time formatting error:', e);
      return 'Invalid Time';
    }
  },

  // Format date and time for case display
  formatCaseDateTime: (dateTimeString) => {
    try {
      const dateTime = moment(dateTimeString);
      if (!dateTime.isValid()) throw new Error('Invalid date/time');
      return {
        date: dateTime.format('DD/MM/YYYY'),
        time: dateTime.format('HH:mm')
      };
    } catch (e) {
      console.error('DateTime formatting error:', e);
      return {
        date: 'Invalid Date',
        time: 'Invalid Time'
      };
    }
  }
};

// Default export for simple usage
export default getCurrentTime;