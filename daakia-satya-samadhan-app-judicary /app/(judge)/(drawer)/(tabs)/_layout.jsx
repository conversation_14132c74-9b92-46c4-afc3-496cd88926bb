import React, { useEffect } from 'react';
import { Tabs } from 'expo-router';
import { Ionicons } from '@expo/vector-icons';
import CustomHeader from '../../../../components/Larges/CustomHeader';
import { SafeAreaView } from 'react-native-safe-area-context';
import { useAuth } from '../../../../context/auth-context';
import { router } from 'expo-router';

const TabsLayoutOfficer = () => {
  const { selectedRole } = useAuth();
  const handleSearchPress = () => {
    console.log('Search pressed');
  };

  const handleNotificationPress = () => {
    router.push('(screens)/notifications');
  };

  const headerConfigs = {
    index: {
      title: 'Judge Home Page',
      showMenu: true,
      menuColor: '#979797',
      showSearch: false,
      showNotification: true,
      notificationHandler: handleNotificationPress,
      notificationIconName: 'notifications-outline', 
      notificationIconColor: '#979797',
      headerBackgroundColor: '#fff',
      titleColor: '#333333',
    },
    profile: {
      title: 'Profile',
      showMenu: true,
      menuColor: '#979797',
      showNotification: true,
      notificationHandler: handleNotificationPress,
      notificationIconName: 'notifications-outline', 
      notificationIconColor: '#979797',
      headerBackgroundColor: '#fff',
    },
    settings: {
      title: 'Settings',
      showMenu: true,
      menuColor: '#979797',
      showNotification: true,
      notificationHandler: handleNotificationPress,
      notificationIconName: 'notifications-outline', 
      notificationIconColor: '#979797',
      headerBackgroundColor: '#fff',
    },
  };

  return (
    <Tabs
      screenOptions={({ route }) => ({
        tabBarActiveTintColor: '#0546a1',
        tabBarInactiveTintColor: 'gray',
        tabBarStyle: {
          backgroundColor: '#f8f8f8',
          borderTopWidth: 1,
          borderTopColor: '#e7e7e7',
        },
        header: () => (
          <CustomHeader {...headerConfigs[route.name]} />
        ),
      })}
    >
      <Tabs.Screen
        name="index"
        options={{
          title: 'Home',
          tabBarIcon: ({ color, size }) => (
            <Ionicons name="home" size={size} color={color} />
          ),
          tabBarLabel: 'Home',
        }}
      />
      <Tabs.Screen
        name="profile"
        options={{
          title: 'Profile',
          tabBarIcon: ({ color, size }) => (
            <Ionicons name="person" size={size} color={color} />
          ),
          tabBarLabel: 'Profile',
        }}
      />
      <Tabs.Screen
        name="settings"
        options={{
          title: 'Settings',
          tabBarIcon: ({ color, size }) => (
            <Ionicons name="settings" size={size} color={color} />
          ),
          tabBarLabel: 'Settings',
        }}
      />
    </Tabs>
  );
};

export default TabsLayoutOfficer;