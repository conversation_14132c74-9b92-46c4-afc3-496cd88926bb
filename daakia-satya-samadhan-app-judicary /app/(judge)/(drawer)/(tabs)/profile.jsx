import { View, Text, Image, StyleSheet, ScrollView, StatusBar, SafeAreaView } from 'react-native';
import React from 'react';
import { useAuth } from '../../../../context/auth-context';
import { transformUrl } from '../../../../utils/transformUrl';
import { Ionicons } from '@expo/vector-icons';
import { LinearGradient } from 'expo-linear-gradient';
import { Colors } from '../../../../constants/colors';

const ProfileOfficer = () => {
  const { profile, selectedRole } = useAuth();

  if (!profile) {
    return (
      <SafeAreaView style={styles.loadingContainer}>
        <Text style={styles.loadingText}>Loading profile...</Text>
      </SafeAreaView>
    );
  }

  const {
    name,
    category,
    emailId,
    mobileNumber,
    policeProfile,
    roles,
    displayUrl,
  } = profile;
  console.log(profile);

  const transformedImageUrl = transformUrl(displayUrl);

  return (
    <SafeAreaView style={styles.safeArea}>
      <StatusBar style="light" />
      <ScrollView contentContainerStyle={styles.scrollContainer}>
        {/* Enhanced Header with Gradient */}
        <LinearGradient
          colors={[Colors.primary, '#1E4FC7']}
          style={styles.header}
        >
          <View style={styles.headerContent}>
            {transformedImageUrl ? (
              <Image source={{ uri: transformedImageUrl }} style={styles.headerProfileImage} />
            ) : (
              <View style={styles.headerProfileImagePlaceholder}>
                <Text style={styles.headerProfileImagePlaceholderText}>{name?.charAt(0) || "U"}</Text>
              </View>
            )}
            <Text style={styles.headerName}>{name}</Text>
            <Text style={styles.headerRole}>{selectedRole}</Text>
          </View>
        </LinearGradient>

        {/* Enhanced Info Sections */}
        <View style={styles.sectionContainer}>
          <View style={styles.sectionHeaderRow}>
            <Ionicons name="person-circle-outline" size={24} color="#0B36A1" />
            <Text style={styles.sectionTitle}>Personal Information</Text>
          </View>
          
          <View style={styles.infoItem}>
            <Ionicons name="person-outline" size={20} color="#0B36A1" style={styles.infoIcon} />
            <View style={styles.infoContent}>
              <Text style={styles.infoLabel}>Full Name</Text>
              <Text style={styles.infoValue}>{name}</Text>
            </View>
          </View>

          <View style={styles.infoItem}>
            <Ionicons name="mail-outline" size={20} color="#0B36A1" style={styles.infoIcon} />
            <View style={styles.infoContent}>
              <Text style={styles.infoLabel}>Email</Text>
              <Text style={styles.infoValue}>{emailId}</Text>
            </View>
          </View>

          <View style={styles.infoItem}>
            <Ionicons name="call-outline" size={20} color="#0B36A1" style={styles.infoIcon} />
            <View style={styles.infoContent}>
              <Text style={styles.infoLabel}>Mobile</Text>
              <Text style={styles.infoValue}>{mobileNumber}</Text>
            </View>
          </View>
        </View>

        {/* Enhanced Police Profile Section */}
        {policeProfile && (
          <View style={styles.sectionContainer}>
            <View style={styles.sectionHeaderRow}>
              <Ionicons name="shield-checkmark-outline" size={24} color="#0B36A1" />
              <Text style={styles.sectionTitle}>Professional Details</Text>
            </View>
            
            <View style={styles.infoItem}>
              <Ionicons name="shield-outline" size={20} color="#0B36A1" style={styles.infoIcon} />
              <View style={styles.infoContent}>
                <Text style={styles.infoLabel}>Designation</Text>
                <Text style={styles.infoValue}>{policeProfile.designation}</Text>
              </View>
            </View>

            <View style={styles.infoItem}>
              <Ionicons name="business-outline" size={20} color="#0B36A1" style={styles.infoIcon} />
              <View style={styles.infoContent}>
                <Text style={styles.infoLabel}>Department</Text>
                <Text style={styles.infoValue}>{policeProfile.department.name}</Text>
              </View>
            </View>

            <View style={styles.infoItem}>
              <Ionicons name="location-outline" size={20} color="#0B36A1" style={styles.infoIcon} />
              <View style={styles.infoContent}>
                <Text style={styles.infoLabel}>Police Station</Text>
                <Text style={styles.infoValue}>{policeProfile.policeStation.name}, {policeProfile.policeStation.city}</Text>
              </View>
            </View>
          </View>
        )}

        {/* Enhanced Roles Section */}
        <View style={styles.sectionContainer}>
          <View style={styles.sectionHeaderRow}>
            <Ionicons name="key-outline" size={24} color="#0B36A1" />
            <Text style={styles.sectionTitle}>Roles & Permissions</Text>
          </View>
          <View style={styles.rolesContainer}>
            {roles.map((role) => (
              <View 
                key={role} 
                style={[
                  styles.roleTag,
                  role === selectedRole ? styles.selectedRoleTag : null
                ]}
              >
                <Ionicons 
                  name={role === selectedRole ? "checkmark-circle" : "ellipse-outline"} 
                  size={16} 
                  color={role === selectedRole ? "#fff" : "#0B36A1"} 
                  style={styles.roleTagIcon}
                />
                <Text 
                  style={[
                    styles.roleTagText,
                    role === selectedRole ? styles.selectedRoleTagText : null
                  ]}
                >
                  {role}
                </Text>
              </View>
            ))}
          </View>
        </View>
      </ScrollView>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  safeArea: {
    flex: 1,
    backgroundColor: Colors.background,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  loadingText: {
    fontSize: 16,
    color: Colors.primary,
    fontFamily: 'Roboto',
  },
  scrollContainer: {
    flexGrow: 1,
    paddingBottom: 20,
  },
  header: {
    paddingTop: 40,
    paddingBottom: 30,
  },
  headerContent: {
    alignItems: 'center',
  },
  headerProfileImage: {
    width: 100,
    height: 100,
    borderRadius: 50,
    borderWidth: 4,
    borderColor: Colors.background,
  },
  headerProfileImagePlaceholder: {
    width: 100,
    height: 100,
    borderRadius: 50,
    backgroundColor: 'rgba(255,255,255,0.2)',
    justifyContent: 'center',
    alignItems: 'center',
    borderWidth: 4,
    borderColor: Colors.background,
  },
  headerName: {
    fontSize: 24,
    color: Colors.background,
    fontFamily: 'Roboto_bold',
    marginTop: 15,
  },
  headerRole: {
    fontSize: 16,
    color: 'rgba(255,255,255,0.8)',
    marginTop: 5,
  },
  sectionContainer: {
    backgroundColor: Colors.background,
    marginHorizontal: 20,
    marginTop: 20,
    borderRadius: 15,
    padding: 20,
    elevation: 2,
    shadowColor: Colors.black,
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.05,
    shadowRadius: 2,
  },
  sectionHeaderRow: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 20,
  },
  sectionTitle: {
    fontSize: 18,
    color: Colors.primary,
    fontFamily: 'Roboto_bold',
    marginLeft: 10,
  },
  infoItem: {
    flexDirection: 'row',
    paddingVertical: 15,
    borderBottomWidth: 0.2,
    borderBottomColor: Colors.border,
  },
  infoIcon: {
    marginRight: 15,
    marginTop: 2,
    color: Colors.primary,
  },
  infoContent: {
    flex: 1,
  },
  infoLabel: {
    fontSize: 12,
    color: Colors.lightText,
    fontFamily: 'Roboto',
  },
  infoValue: {
    fontSize: 15,
    color: Colors.black,
    marginTop: 2,
    fontFamily: 'Roboto',
  },
  rolesContainer: {
    flexDirection: 'row',
    flexWrap: 'wrap',
  },
  roleTag: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#f0f4ff',
    paddingHorizontal: 15,
    paddingVertical: 8,
    borderRadius: 20,
    marginRight: 10,
    marginBottom: 10,
  },
  roleTagIcon: {
    marginRight: 6,
  },
  roleTagText: {
    color: Colors.primary,
    fontSize: 13,
    fontFamily: 'Roboto',
  },
  selectedRoleTag: {
    backgroundColor: Colors.primary,
  },
  selectedRoleTagText: {
    color: Colors.background,
    fontFamily: 'Roboto_bold',
  }
});

export default ProfileOfficer;