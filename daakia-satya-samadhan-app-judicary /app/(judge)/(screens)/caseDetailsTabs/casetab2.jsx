import React, { useState, useEffect, useCallback } from 'react';
import { View, Text, FlatList, Image, StyleSheet, TouchableOpacity, ActivityIndicator, Alert, Linking, Platform, Modal } from 'react-native';
import MaterialCommunityIcons from "@expo/vector-icons/MaterialCommunityIcons";
import { useAuth } from "../../../../context/auth-context";
import { transformUrl } from "../../../../utils/transformUrl";
import * as FileSystem from 'expo-file-system';
import * as IntentLauncher from 'expo-intent-launcher';
import * as Sharing from 'expo-sharing';
import * as WebBrowser from 'expo-web-browser';
import PreviewComponent from '../../../../components/Larges/PreviewComponent';
import { Colors } from '../../../../constants/colors';
import { apiService } from "../../../../services/api";



const Tab2 = ({ caseid, changeTab }) => {
  const [reports, setReports] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [downloadingId, setDownloadingId] = useState(null);
  const [previewUri, setPreviewUri] = useState(null);
  const [showPreview, setShowPreview] = useState(false);
  const { token } = useAuth();

  const fetchForensicReports = useCallback(async () => {
    if (!caseid) {
      setError('Case ID is missing');
      setLoading(false);
      return;
    }

    try {
      const result = await apiService.fetchForensicReports(token, caseid);

      if (result && result.data) {
        setReports(result.data);
      } else {
        setReports([]);
      }
      setLoading(false);
    } catch (err) {
      console.error('Error fetching forensic reports:', err);
      setError('Failed to fetch forensic reports');
      setLoading(false);
    }
  }, [caseid, token]);

  useEffect(() => {
    fetchForensicReports();
  }, [fetchForensicReports]);

  const downloadAndOpenPdf = async (url, title) => {
    if (!url) {
      Alert.alert('Error', 'No file URL provided');
      return;
    }
    
    try {
      setDownloadingId(`${title}-${url}`); // Unique ID for each URL
      const transformedUrl = transformUrl(url);
      const filename = url.split('/').pop();
      const localUri = `${FileSystem.documentDirectory}${filename}`;
      
      const downloadResumable = FileSystem.createDownloadResumable(
        transformedUrl,
        localUri,
        {},
        (downloadProgress) => {
          const progress = downloadProgress.totalBytesWritten / downloadProgress.totalBytesExpectedToWrite;
          console.log(`Download progress: ${progress * 100}%`);
        }
      );
      
      const { uri } = await downloadResumable.downloadAsync();
      
      if (Platform.OS === 'ios') {
        const canShare = await Sharing.isAvailableAsync();
        if (canShare) {
          await Sharing.shareAsync(uri);
        } else {
          await WebBrowser.openBrowserAsync(transformedUrl);
        }
      } else if (Platform.OS === 'android') {
        const contentUri = await FileSystem.getContentUriAsync(uri);
        await IntentLauncher.startActivityAsync('android.intent.action.VIEW', {
          data: contentUri,
          flags: 1,
          type: 'application/pdf',
        });
      }
    } catch (error) {
      console.error('Error downloading or opening PDF:', error);
      Alert.alert('Error', 'Could not download or open the PDF file');
    } finally {
      setDownloadingId(null);
    }
  };

  const handleAttachmentClick = useCallback((url, title) => {
    if (!url) {
      Alert.alert('Error', 'No file URL provided');
      return;
    }

    const isPdf = url.toLowerCase().endsWith('.pdf');
    const isImageFile = /\.(jpg|jpeg|png|gif|webp)$/i.test(url);

    if (isPdf) {
      downloadAndOpenPdf(url, title);
    } else if (isImageFile) {
      setPreviewUri(transformUrl(url));
      setShowPreview(true);
    } else {
      const transformedUrl = transformUrl(url);
      WebBrowser.openBrowserAsync(transformedUrl);
    }
  }, []);

  const closePreview = useCallback(() => {
    setShowPreview(false);
    setPreviewUri(null);
  }, []);

  const handleEvidenceTab = useCallback(() => {
    if (typeof changeTab === 'function') {
      changeTab('tab1');
    }
  }, [changeTab]);

  const renderReportMedia = useCallback((url) => {
    if (!url) return <View style={styles.evidenceImage} />;

    const transformedUrl = transformUrl(url);
    const isDocument = /\.(pdf|doc|docx|txt|xls|xlsx)$/i.test(url);
    const isPdf = /\.pdf$/i.test(url);

    if (isPdf) {
      return (
        <View style={styles.evidenceImage}>
          <Image
            source={require('../../../../assets/images/pdf-icon.png')}
            style={styles.pdfThumbnail}
            resizeMode="cover"
          />
          <View style={styles.videoOverlay}>
            <Text style={styles.videoLabel}>PDF</Text>
          </View>
        </View>
      );
    } else if (isDocument) {
      return (
        <View style={styles.evidenceImage}>
          <MaterialCommunityIcons 
            name="file-document" 
            size={30} 
            color="#FFFFFF" 
            style={styles.playIcon} 
          />
          <View style={styles.videoOverlay}>
            <Text style={styles.videoLabel}>DOC</Text>
          </View>
        </View>
      );
    } else {
      return (
        <Image
          source={{ uri: transformedUrl }}
          style={styles.evidenceImage}
          defaultSource={require('../../../../assets/images/small_satya_smadhanLogo.png')}
        />
      );
    }
  }, []);

  const renderItem = useCallback(({ item }) => {
    switch (item.type) {
      case 'details':
        return (
          <View style={styles.detailsContainer}>
            <View style={styles.segmentedControlContainer}>
              <View style={styles.segmentedControl}>
                <TouchableOpacity
                  style={styles.segmentButton}
                  onPress={handleEvidenceTab}
                >
                  <MaterialCommunityIcons name="file-document-multiple-outline" size={18} color="#666666" />
                  <Text style={styles.segmentText}>Evidences</Text>
                </TouchableOpacity>
                <TouchableOpacity
                  style={[styles.segmentButton, styles.activeSegment]}
                >
                  <MaterialCommunityIcons name="clipboard-text-outline" size={18} color="#FFFFFF" />
                  <Text style={styles.activeSegmentText}>Reports</Text>
                </TouchableOpacity>
              </View>
            </View>
          </View>
        );
      case 'reports':
        return (
          <View style={styles.sectionContainer}>
            <Text style={styles.sectionTitle}>Forensic Reports</Text>
            {reports && reports.length > 0 ? (
              reports.map((report, index) => {
                if (!report) return null;

                const labName = report.forensicRequestId?.labId?.name || 'Unknown Lab';

                return (
                  <View
                    key={report._id || index}
                    style={styles.evidenceCard}
                  >
                    <View style={styles.evidenceDetails}>
                      <View style={styles.titleContainer}>
                        <Text style={styles.evidenceTitle}>{report.title || 'Untitled Report'}</Text>
                      </View>
                      {report.description && (
                        <Text style={styles.evidenceDescription} numberOfLines={2}>
                          {report.description}
                        </Text>
                      )}
                      <Text style={styles.labName}>
                        Lab: {labName}
                      </Text>
                      <Text style={styles.reportDate}>
                        {new Date(report.createdAt).toLocaleDateString()}
                      </Text>
                    </View>
                    <View style={styles.attachmentsContainer}>
                      {report.attachmentUrl && report.attachmentUrl.length > 0 ? (
                        report.attachmentUrl.map((url, idx) => (
                          <TouchableOpacity
                            key={idx}
                            style={styles.attachmentItem}
                            onPress={() => handleAttachmentClick(url, report.title)}
                          >
                            {renderReportMedia(url)}
                            <TouchableOpacity
                              style={styles.shareButton}
                              onPress={() => handleAttachmentClick(url, report.title)}
                              disabled={downloadingId === `${report.title}-${url}`}
                            >
                              {downloadingId === `${report.title}-${url}` ? (
                                <ActivityIndicator size="small" color="#0B36A1" />
                              ) : (
                                <MaterialCommunityIcons
                                  name={url.toLowerCase().endsWith('.pdf') ? "file-pdf-box" : "file-download"}
                                  size={24}
                                  color="#0B36A1"
                                />
                              )}
                            </TouchableOpacity>
                          </TouchableOpacity>
                        ))
                      ) : (
                        <Text style={styles.noEvidenceText}>No attachments found</Text>
                      )}
                    </View>
                  </View>
                );
              })
            ) : (
              <Text style={styles.noEvidenceText}>No reports found</Text>
            )}
          </View>
        );
      default:
        return null;
    }
  }, [reports, handleEvidenceTab, handleAttachmentClick, renderReportMedia, downloadingId]);

  if (error) {
    return (
      <View style={styles.errorContainer}>
        <MaterialCommunityIcons name="alert-circle-outline" size={48} color="#FF3B30" />
        <Text style={styles.errorText}>Error: {error}</Text>
        <TouchableOpacity style={styles.retryButton} onPress={fetchForensicReports}>
          <Text style={styles.retryButtonText}>Retry</Text>
        </TouchableOpacity>
      </View>
    );
  }

  if (loading) {
    return (
      <View style={styles.fullScreenLoader}>
        <ActivityIndicator size="large" color="#0B36A1" />
      </View>
    );
  }

  return (
    <View style={styles.container}>
      <FlatList
        data={[{ type: 'details' }, { type: 'reports' }]}
        renderItem={renderItem}
        keyExtractor={(item, index) => `${item.type}-${index}`}
        contentContainerStyle={{ flexGrow: 1 }}
        initialNumToRender={2}
      />
      <Modal
        visible={showPreview}
        transparent={true}
        animationType="fade"
        onRequestClose={closePreview}
      >
        <PreviewComponent 
          uri={previewUri} 
          onClose={closePreview} 
        />
      </Modal>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: "#FFFFFF",
  },
  detailsContainer: {
    paddingHorizontal: '2%',
    paddingVertical: 10,
  },
  sectionContainer: {
    marginTop: 10,
  },
  sectionTitle: {
    textAlign: 'left',
    color: Colors.primary,
    fontWeight: '500',
    fontSize: 18,
    marginBottom: 14,
    marginHorizontal: '3%',
    fontFamily: 'Roboto_bold',
  },
  evidenceCard: {
    borderColor: Colors.border,
    borderWidth: 1,
    marginHorizontal: '2%',
    marginBottom: 24,
    padding: 12,
    borderRadius: 10,
  },
  evidenceDetails: {
    marginBottom: 12,
  },
  evidenceTitle: {
    fontSize: 15,
    fontWeight: '500',
    color: Colors.black,
    marginBottom: 4,
  },
  evidenceDescription: {
    fontSize: 12,
    color: Colors.lightText,
    marginBottom: 8,
    fontFamily: 'Roboto',
  },
  labName: {
    fontSize: 12,
    color: Colors.primary,
    fontWeight: '500',
    marginTop: 4,
    fontFamily: 'Roboto',
  },
  reportDate: {
    fontSize: 12,
    color: Colors.lightText,
    marginTop: 4,
    fontFamily: 'Roboto',
  },
  attachmentsContainer: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    justifyContent: 'flex-start',
  },
  attachmentItem: {
    width: 100,
    marginRight: 10,
    marginBottom: 10,
    alignItems: 'center',
  },
  evidenceImage: {
    height: 80,
    width: 80,
    borderRadius: 8,
    backgroundColor: '#f0f0f0',
    justifyContent: 'center',
    alignItems: 'center',
    overflow: 'hidden',
  },
  pdfThumbnail: {
    width: 80,
    height: 80,
    borderRadius: 8,
  },
  shareButton: {
    padding: 4,
    justifyContent: 'center',
    alignItems: 'center',
    marginTop: 8,
  },
  segmentedControlContainer: {
    alignItems: 'flex-start',
    marginBottom: 24,
  },
  segmentedControl: {
    flexDirection: 'row',
    borderRadius: 30,
    overflow: 'hidden',
    borderWidth: 1,
    borderColor: Colors.border,
    backgroundColor: '#E8E8E8',
  },
  segmentButton: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 10,
    paddingHorizontal: 20,
    justifyContent: 'center',
    width: 150,
  },
  activeSegment: {
    backgroundColor: '#367E18',
    borderRadius: 30,
  },
  segmentText: {
    color: '#666666',
    marginLeft: 8,
    fontWeight: '500',
  },
  activeSegmentText: {
    color: '#FFFFFF',
    marginLeft: 8,
    fontWeight: '500',
  },
  noEvidenceText: {
    textAlign: 'center',
    color: Colors.lightText,
    fontStyle: 'italic',
    marginTop: 20,
    marginBottom: 40,
    fontFamily: 'Roboto',
  },
  errorContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 20,
    backgroundColor: '#FFFFFF',
  },
  errorText: {
    color: '#FF3B30',
    marginVertical: 16,
    textAlign: 'center',
    fontSize: 16,
  },
  retryButton: {
    backgroundColor: '#0B36A1',
    paddingVertical: 10,
    paddingHorizontal: 20,
    borderRadius: 8,
  },
  retryButtonText: {
    color: '#FFFFFF',
    fontSize: 16,
    fontWeight: '500',
    fontFamily: 'Roboto',
  },
  fullScreenLoader: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: '#FFFFFF',
  },
  videoOverlay: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    backgroundColor: 'rgba(0, 0, 0, 0.4)',
    justifyContent: 'center',
    alignItems: 'center',
    borderRadius: 8,
  },
  videoLabel: {
    color: '#FFFFFF',
    fontWeight: 'bold',
    fontSize: 12,
    backgroundColor: 'rgba(0, 0, 0, 0.6)',
    paddingHorizontal: 6,
    paddingVertical: 3,
    borderRadius: 4,
    fontFamily: 'Roboto',
  },
  playIcon: {
    position: 'absolute',
    top: '50%',
    left: '50%',
    marginLeft: -15,
    marginTop: -15,
    zIndex: 10,
  },
  titleContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    marginBottom: 4,
  }
});

export default React.memo(Tab2);