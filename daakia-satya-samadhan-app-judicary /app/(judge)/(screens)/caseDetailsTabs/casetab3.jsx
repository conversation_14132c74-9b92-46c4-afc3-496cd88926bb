import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  Alert,
  Modal,
  ActivityIndicator,
  Platform,
} from 'react-native';
import { GestureHandlerRootView } from 'react-native-gesture-handler';
import CapturePageHeader from '../../../../components/Smalls/CapturePageHeader';
import CaptureButton from '../../../../components/Smalls/CaptureButton';
import OptionsOverlay from '../../../../components/Smalls/OptionsOverlay';
import MediaGrid from '../../../../components/Smalls/MediaGrid';
import { apiService } from '../../../../services/api';
import { useAuth } from '../../../../context/auth-context';
import MaterialCommunityIcons from "@expo/vector-icons/MaterialCommunityIcons";
import { transformUrl } from '../../../../utils/transformUrl';
import * as FileSystem from 'expo-file-system';
import * as IntentLauncher from 'expo-intent-launcher';
import * as Sharing from 'expo-sharing';
import * as WebBrowser from 'expo-web-browser';
import PreviewComponent from '../../../../components/Larges/PreviewComponent';
import { Colors } from '../../../../constants/colors';
import { router } from 'expo-router';


const Tab3 = ({ caseid }) => {
  const [showOptions, setShowOptions] = useState(false);
  const [media, setMedia] = useState([]);
  const [events, setEvents] = useState([]);
  const [loading, setLoading] = useState(true);
  const { token } = useAuth();
  const [downloadingId, setDownloadingId] = useState(null);
  const [previewUri, setPreviewUri] = useState(null);
  const [showPreview, setShowPreview] = useState(false);
  const [isSubmitting, setIsSubmitting] = useState(false);

  useEffect(() => {
    if (token) {
      fetchEvents();
    }
  }, [caseid, token]);

  const fetchEvents = async () => {
    try {
      const result = await apiService.fetchCaseEvents(token, caseid);
      console.log('Case Events Result:', JSON.stringify(result, null, 2));
      if (result.status === 'success') {
        setEvents(result.data || []);
      } else {
        Alert.alert('Error', 'Failed to fetch case events');
      }
    } catch (error) {
      console.error('Error fetching events:', error);
      Alert.alert('Error', 'Failed to fetch case events');
    } finally {
      setLoading(false);
    }
  };

  const handleUploadOption = (option) => {
    setShowOptions(false);
    Alert.alert('Coming Soon', 'This feature will be available soon!');
  };

  const handleDeleteMedia = (index) => {
    setMedia((prevMedia) => prevMedia.filter((_, i) => i !== index));
  };

  const downloadAndOpenPdf = async (url, title) => {
    if (!url) {
      Alert.alert('Error', 'No file URL provided');
      return;
    }
    try {
      setDownloadingId(`${title}-${url}`);
      const transformedUrl = transformUrl(url);
      const filename = url.split('/').pop();
      const localUri = `${FileSystem.documentDirectory}${filename}`;
      const downloadResumable = FileSystem.createDownloadResumable(
        transformedUrl,
        localUri,
        {},
        (downloadProgress) => {
          const progress = downloadProgress.totalBytesWritten / downloadProgress.totalBytesExpectedToWrite;
          console.log(`Download progress: ${progress * 100}%`);
        }
      );
      const { uri } = await downloadResumable.downloadAsync();
      if (Platform.OS === 'ios') {
        const canShare = await Sharing.isAvailableAsync();
        if (canShare) {
          await Sharing.shareAsync(uri);
        } else {
          await WebBrowser.openBrowserAsync(transformedUrl);
        }
      } else if (Platform.OS === 'android') {
        const contentUri = await FileSystem.getContentUriAsync(uri);
        await IntentLauncher.startActivityAsync('android.intent.action.VIEW', {
          data: contentUri,
          flags: 1,
          type: 'application/pdf',
        });
      }
    } catch (error) {
      console.error('Error downloading or opening PDF:', error);
      Alert.alert('Error', 'Could not download or open the PDF file');
    } finally {
      setDownloadingId(null);
    }
  };

  const handleAttachmentClick = (url, title) => {
    if (!url) {
      Alert.alert('Error', 'No file URL provided');
      return;
    }
    const isImageFile = /\.(jpg|jpeg|png|gif|webp)$/i.test(url);
    if (isImageFile) {
      setPreviewUri(transformUrl(url));
      setShowPreview(true);
    } else {
      downloadAndOpenPdf(url, title);
    }
  };

  const closePreview = () => {
    setShowPreview(false);
    setPreviewUri(null);
  };

  return (
    <GestureHandlerRootView style={{ flex: 1 }}>
      <View style={styles.container}>
        <ScrollView style={styles.content} contentContainerStyle={{ paddingBottom: 1 }}>
  
          {/* <CapturePageHeader
            title="Upload Documents"
            subtitle="Please upload the document ensuring it includes all supporting docs, recordings and the official seal."
          /> */}

         

          {/* {showOptions && (
            <OptionsOverlay
              visible={showOptions} 
              onSelectOption={handleUploadOption} 
              onClose={() => setShowOptions(false)} 
            />
          )}

          {media.length > 0 && (
            <MediaGrid
              media={media}
              onDeleteMedia={handleDeleteMedia}
              thumbnailSize={150}
            />
          )} */}

          {/* Display Events */}
          <View style={styles.eventsContainer}>
            <Text style={styles.eventsTitle}>Court Proceedings</Text>
            {loading ? (
              <Text>Loading events...</Text>
            ) : events.length > 0 ? (
              events.map((event, idx) => {
                // Support both string and array for attachmentUrl
                let attachments = [];
                if (Array.isArray(event.attachmentUrl)) {
                  attachments = event.attachmentUrl.filter(Boolean);
                } else if (typeof event.attachmentUrl === 'string' && event.attachmentUrl) {
                  attachments = [event.attachmentUrl];
                }
                const maxChips = 4;
                const extraCount = attachments.length - maxChips;
                return (
                  <View key={event._id || idx} style={styles.card}>
                    <TouchableOpacity 
                      onPress={() => {
                        router.push({
                          pathname: '(screens)/hearingEvents',
                          params: { 
                            eventDetails: JSON.stringify({
                              eventId: event._id,
                              title: event.title,
                              startDateTime: event.startDateTime,
                              endDateTime: event.endDateTime,
                              attachmentUrl: event.attachmentUrl
                            })
                          }
                        });
                      }}
                    >
                      <Text style={styles.cardTitle}>{event.title}</Text>
                      <Text style={styles.cardDate}>{
                        event.startDateTime && event.endDateTime
                          ? `${new Date(event.startDateTime).toLocaleString([], { weekday: 'long', year: 'numeric', month: 'long', day: 'numeric' })} ${new Date(event.startDateTime).toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' })} - ${new Date(event.endDateTime).toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' })}`
                          : ''
                      }</Text>
                      <Text style={styles.contentLabel}>Content</Text>
                      <View style={styles.chipRow}>
                        {attachments.slice(0, maxChips).map((url, i) => (
                          <TouchableOpacity
                            key={i}
                            style={styles.chip}
                            onPress={() => handleAttachmentClick(url, event.title)}
                            disabled={downloadingId === `${event.title}-${url}`}
                          >
                            <MaterialCommunityIcons
                              name={url.toLowerCase().endsWith('.pdf') ? 'file-pdf-box' : 'file-document'}
                              size={18}
                              color={Colors.primary}
                              style={{ marginRight: 4 }}
                            />
                            <Text style={styles.chipText} numberOfLines={1}>
                              {url.split('/').pop().split('.')[0]}
                            </Text>
                            {downloadingId === `${event.title}-${url}` && (
                              <ActivityIndicator size="small" color={Colors.primary} style={{ marginLeft: 4 }} />
                            )}
                          </TouchableOpacity>
                        ))}
                        {extraCount > 0 && (
                          <View style={styles.chip}>
                            <Text style={[styles.chipText, { color: Colors.primary }]}>+{extraCount}</Text>
                          </View>
                        )}
                      </View>
                    </TouchableOpacity>
                  </View>
                );
              })
            ) : (
              <Text>No events found for this case</Text>
            )}
          </View>

          {/* Schedule Hearing Button */}
          <View style={styles.scheduleHearingContainer}>
            <View style={styles.buttonRow}>
              <TouchableOpacity
                style={[
                  styles.commentButton,
                  styles.scheduleHearingButton,
                  isSubmitting && styles.disabledButton
                ]}
                onPress={() => {
                  router.push({
                    pathname: '(screens)/scheduleHearing',
                    params: { caseid },
                  });
                }}
                disabled={isSubmitting}
              >
                <Text style={styles.commentButtonText}>SCHEDULE HEARING</Text>
              </TouchableOpacity>
            </View>
          </View>

          <Modal
            visible={showPreview}
            transparent={true}
            animationType="fade"
            onRequestClose={closePreview}
          >
            <PreviewComponent 
              uri={previewUri} 
              onClose={closePreview} 
            />
          </Modal>
        </ScrollView>
      </View>
    </GestureHandlerRootView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#fff',
  },
  content: {
    flex: 1,
  },
  eventsContainer: {
    paddingHorizontal: 10,
  },
  eventsTitle: {
    fontSize: 18,
    fontWeight: '500',
    marginBottom: 16,

    fontFamily: 'Roboto_bold',
    color: Colors.primary,
  },
  eventItem: {
    backgroundColor: '#f5f5f5',
    padding: 16,
    borderRadius: 8,
    marginBottom: 12,
  },
  eventTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    marginBottom: 8,
  },
  eventDescription: {
    fontSize: 14,
    color: '#666',
    marginBottom: 8,
  },
  eventDate: {
    fontSize: 12,
    color: '#999',
  },
  heading: {
    fontSize: 18,
    fontWeight: '500',
    color: Colors.primary,
    marginBottom: 16,
    marginHorizontal: '3%',
    fontFamily: 'Roboto_bold',
  },
  card: {
    backgroundColor: '#fff',
    borderRadius: 12,
    padding: 16,
    marginBottom: 18,
    marginHorizontal: 4,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.08,
    shadowRadius: 4,
    elevation: 2,
  },
  cardTitle: {
    fontSize: 18,
    fontWeight: '500',
    color: Colors.black,
    marginBottom: 4,
    fontFamily: 'Roboto_medium',

  },
  cardDate: {
    fontSize: 14,
    color: Colors.lightText,
    marginBottom: 10,
  },
  contentLabel: {
    fontSize: 13,
    color: Colors.lightText,
    marginBottom: 8,
    fontWeight: 'bold',
  },
  chipRow: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    alignItems: 'center',
    marginBottom: 4,
  },
  chip: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#F5F5F5',
    borderRadius: 16,
    paddingHorizontal: 10,
    paddingVertical: 6,
    marginRight: 8,
    marginBottom: 6,
    minWidth: 40,
  },
  chipText: {
    fontSize: 13,
    color: Colors.black,
    maxWidth: 90,
  },
  scheduleHearingContainer: {
    paddingHorizontal: '5%',
    paddingVertical: 20,
    marginBottom: 20,
    alignItems: 'center',
  },
  buttonRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    width: '100%',
    gap: 10,
  },
  commentButton: {
    padding: 16,
    borderRadius: 50,
    alignItems: 'center',
    marginBottom: 16,
    flex: 1,
    minHeight: 50,
    justifyContent: 'center',
  },
  scheduleHearingButton: {
    backgroundColor: Colors.primary,
  },
  disabledButton: {
    backgroundColor: Colors.disabled,
    opacity: 0.7,
  },
  commentButtonText: {
    color: Colors.background,
    fontSize: 16,
    fontWeight: '500',
  },
});

export default Tab3; 