import { View, Text, StyleSheet, TouchableOpacity, ScrollView, ActivityIndicator, Alert } from 'react-native'
import React, { useState, useEffect } from 'react'
import { useLocalSearchParams } from 'expo-router'
import MediaGrid from '../../../components/Smalls/MediaGrid'
import EventDetailsCard from '../../../components/Larges/EventDetailsCard'
import OptionsOverlay from '../../../components/Smalls/OptionsOverlay'
import EditEventModal from '../../../components/DispatcherComponets/CalendarCompoProsecutor/EditEventModal'
import * as DocumentPicker from 'expo-document-picker'
import { Colors } from '../../../constants/colors'
import SuccessScreen from '../../../components/Smalls/SuccessScreen'

import { apiService } from '../../../services/api'
import { useAuth } from '../../../context/auth-context'
import useUploadMedia from '../../../hooks/useUploadMedia'

const HearingEvents = () => {
  const params = useLocalSearchParams()
  const { token } = useAuth()
  const { uploadMedia, isUploading } = useUploadMedia()
  const [isOptionsModalVisible, setIsOptionsModalVisible] = useState(false)
  const [isEditModalVisible, setIsEditModalVisible] = useState(false)
  const [isSavingEdit, setIsSavingEdit] = useState(false)
  const [uploadedFiles, setUploadedFiles] = useState([])
  const [eventData, setEventData] = useState(null)
  const [isLoading, setIsLoading] = useState(false)
  const [eventError, setEventError] = useState(null)
  const [showSuccessScreen, setShowSuccessScreen] = useState(false)
  const [successMessage, setSuccessMessage] = useState('')

  // Parse event details from params
  const eventDetails = params.eventDetails ? JSON.parse(params.eventDetails) : null
  // console.log('Event Details:', eventDetails)

  // Fetch event details by ID if eventId is available
  const fetchEventDetails = async () => {
    if (!eventDetails?.eventId || !token) {
      console.log('No eventId or token available for fetching event details')
      return
    }
    setIsLoading(true)
    setEventError(null)
    try {
      const response = await apiService.fetchEventById(token, eventDetails.eventId)
      if (response.status === 'success') {
        setEventData(response.data)
        console.log('Event data fetched successfully:', JSON.stringify(response.data, null, 2))
      } else {
        setEventError(response.message || 'Failed to fetch event details')
        console.error('Failed to fetch event:', response.message)
      }
    } catch (error) {
      setEventError('Error fetching event details: ' + error.message)
      console.error('Error fetching event details:', error)
    } finally {
      setIsLoading(false)
    }
  }

  useEffect(() => {
    fetchEventDetails()
  }, [eventDetails?.eventId, token])

  const handleFileUpload = async () => {
    try {
      const result = await DocumentPicker.getDocumentAsync({
        type: ['application/pdf', 'image/*', 'application/msword', 'application/vnd.openxmlformats-officedocument.wordprocessingml.document'],
        copyToCacheDirectory: true
      })

      if (result.canceled) {
        console.log('File picking cancelled')
        return
      }

      const file = result.assets[0]
      console.log('Selected file:', file)

      // Upload the file using useUploadMedia hook
      const fileUrl = await uploadMedia(file.uri, 'file')

      if (fileUrl) {
        setUploadedFiles(prev => [...prev, {
          uri: file.uri,
          name: file.name,
          type: file.mimeType || 'file',
          size: file.size,
          url: fileUrl
        }])
      } else {
        throw new Error('Failed to upload file')
      }

    } catch (error) {
      console.error('Error uploading file:', error)
      Alert.alert('Error', 'Failed to upload file. Please try again.')
    }
  }

  // Media items for grid (only uploaded files)
  const mediaItems = uploadedFiles.map((item, idx) => ({ ...item, _origin: 'pdf', _idx: idx }))

  // Delete handler for uploaded files
  const handleDeleteMedia = (index) => {
    setUploadedFiles(prev => prev.filter((_, i) => i !== index))
  }

  // Handle opening edit modal
  const handleOpenEditModal = () => {
    setIsEditModalVisible(true)
  }

  // Handle saving edited event details
  const handleSaveEditedEvent = async (updateData) => {
    if (!eventData?._id) {
      Alert.alert('Error', 'Event data not available. Cannot save changes.')
      return
    }

    setIsSavingEdit(true)
    try {
      console.log('=== EVENT EDIT DEBUG ===')
      console.log('Event ID:', eventData._id)
      console.log('Update Payload:', updateData)

      const response = await apiService.updateEvent(token, eventData._id, updateData)

      console.log('Edit Response:', response)

      if (response.status === 'success') {
        setSuccessMessage('Event details updated successfully!')
        setShowSuccessScreen(true)
        setIsEditModalVisible(false)
        // Refresh event data
        await fetchEventDetails()
      } else {
        throw new Error(response.message || 'Failed to update event')
      }
    } catch (error) {
      console.error('Error updating event:', error)
      Alert.alert('Error', `Failed to update event: ${error.message}`)
    } finally {
      setIsSavingEdit(false)
    }
  }

  const handleSubmitMedia = async () => {
    if (mediaItems.length === 0) return
    if (!eventData?._id) {
      Alert.alert('Error', 'Event data not available. Cannot submit media.')
      return
    }
    setIsLoading(true)
    try {
      // Upload all files in parallel
      const uploadPromises = mediaItems.map((item, index) => {
        return uploadMedia(item.uri, 'file')
          .then(fileUrl => {
            if (!fileUrl) throw new Error(`Failed to upload file ${index + 1}`)
            return fileUrl
          })
          .catch(err => {
            console.error(`Failed to upload file ${index + 1}:`, err)
            return null
          })
      })
      const uploadResults = await Promise.all(uploadPromises)
      const successfulUploads = uploadResults.filter(Boolean)
      let existingUrls = []
      if (eventData.attachmentUrl && eventData.attachmentUrl.trim() !== '') {
        existingUrls = eventData.attachmentUrl.split(',').map(s => s.trim()).filter(Boolean)
      }
      const allUrls = [...existingUrls, ...successfulUploads]
      const newAttachmentUrl = allUrls.join(',')
      
      console.log('=== EVENT UPDATE DEBUG ===')
      console.log('Event ID:', eventData._id)
      console.log('Existing URLs:', existingUrls)
      console.log('New Uploaded URLs:', successfulUploads)
      console.log('Combined Attachment URL:', newAttachmentUrl)
      
      // Only update the attachmentUrl field
      const updateData = { attachmentUrl: newAttachmentUrl }
      console.log('Update Payload:', updateData)
      
      const updateRes = await apiService.updateEvent(token, eventData._id, updateData)
      
      console.log('Update Response:', updateRes)
      
      if (updateRes.status === 'success') {
        setSuccessMessage(`${successfulUploads.length} files uploaded successfully!`)
        setShowSuccessScreen(true)
        setUploadedFiles([])
        // Refresh event data
        await fetchEventDetails()
      } else {
        throw new Error(updateRes.message || 'Failed to update event')
      }
    } catch (error) {
      console.error('Error submitting files:', error)
      Alert.alert('Upload Failed', `Failed to upload files: ${error.message}. Please try again.`)
    } finally {
      setIsLoading(false)
    }
  }

  return (
    <View style={styles.container}>
      {/* Loading Overlay */}
      {isLoading && (
        <View style={styles.loadingOverlay}>
          <ActivityIndicator size="large" color={Colors.primary} />
        </View>
      )}

      {/* Success Screen */}
      {showSuccessScreen && (
        <SuccessScreen
          message={successMessage}
          duration={2000}
          onComplete={() => setShowSuccessScreen(false)}
        />
      )}

      <ScrollView style={styles.scrollContainer} showsVerticalScrollIndicator={false}>
        {/* Event Details Section */}
        {isLoading ? null : eventError ? (
          <View style={styles.errorContainer}>
            <Text style={styles.errorText}>Error: {eventError}</Text>
            <TouchableOpacity
              style={styles.retryButton}
              onPress={() => {
                setEventError(null)
                fetchEventDetails()
              }}
            >
              <Text style={styles.retryButtonText}>Retry</Text>
            </TouchableOpacity>
          </View>
        ) : (eventData || eventDetails) ? (
          <View style={styles.cardContainer}>
            <View style={styles.cardHeader}>
              <Text style={styles.cardTitle}>Event Details</Text>
              <TouchableOpacity
                style={styles.addButton}
                onPress={() => setIsOptionsModalVisible(true)}
              >
                <Text style={styles.addButtonText}>+</Text>
              </TouchableOpacity>
            </View>
            <EventDetailsCard
              event={eventData || eventDetails}
              showEditButton={false}
              containerStyle={styles.eventCard}
            />
          </View>
        ) : null}

        {/* Files Preview Section */}
        <View style={styles.contentContainer}>
          {mediaItems.length > 0 && (
            <View style={styles.mediaContainer}>
              <Text style={styles.subsectionTitle}>Files</Text>
              <MediaGrid
                media={mediaItems}
                onDeleteMedia={handleDeleteMedia}
                thumbnailSize={150}
              />
            </View>
          )}
        </View>
      </ScrollView>

      {/* Submit Button - always visible at bottom */}
      <TouchableOpacity
        style={[styles.submitButton, mediaItems.length === 0 && styles.disabledButton]}
        onPress={handleSubmitMedia}
        disabled={mediaItems.length === 0 || isLoading}
      >
        <Text style={styles.buttonText}>{isLoading ? 'Submitting...' : 'Submit'}</Text>
      </TouchableOpacity>

      {/* Options Overlay */}
      <OptionsOverlay
        visible={isOptionsModalVisible}
        onSelectOption={(option) => {
          setIsOptionsModalVisible(false)
          if (option === 'pdf') {
            handleFileUpload()
          } else if (option === 'edit') {
            handleOpenEditModal()
          }
        }}
        onClose={() => setIsOptionsModalVisible(false)}
        options={[
          { id: 'edit', label: 'Edit Event Details' },
          { id: 'pdf', label: 'Upload File' }
        ]}
      />

      {/* Edit Event Modal */}
      <EditEventModal
        visible={isEditModalVisible}
        onClose={() => setIsEditModalVisible(false)}
        onSave={handleSaveEditedEvent}
        eventData={eventData || eventDetails}
        loading={isSavingEdit}
      />
    </View>
  )
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: Colors.background,
    padding: 20,
  },
  scrollContainer: {
    flex: 1,
  },
  cardContainer: {
    marginBottom: 20,
  },
  cardHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 15,
    paddingHorizontal: 5,
  },
  cardTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    color: Colors.black,
  },
  addButton: {
    backgroundColor: Colors.primary,
    width: 40,
    height: 40,
    borderRadius: 20,
    justifyContent: 'center',
    alignItems: 'center',
    elevation: 3,
    shadowColor: Colors.black,
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.25,
    shadowRadius: 3.84,
  },
  addButtonText: {
    color: Colors.background,
    fontSize: 28,
    fontWeight: '600',
    marginTop: -2,
  },
  contentContainer: {
    padding: 20,
  },
  mediaContainer: {
    marginBottom: 20,
  },
  sectionTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    marginBottom: 15,
    color: Colors.black,
  },
  subsectionTitle: {
    fontSize: 16,
    fontWeight: '600',
    marginBottom: 12,
    color: Colors.lightText,
  },
  noVideosText: {
    fontSize: 16,
    color: Colors.lightText,
    textAlign: 'center',
    marginBottom: 20,
    fontStyle: 'italic',
  },
  loadingContainer: {
    padding: 40,
    alignItems: 'center',
    justifyContent: 'center',
  },
  loadingText: {
    marginTop: 12,
    fontSize: 16,
    color: Colors.lightText,
    textAlign: 'center',
  },
  errorContainer: {
    padding: 20,
    margin: 16,
    backgroundColor: '#FFF5F5',
    borderRadius: 8,
    borderWidth: 1,
    borderColor: '#FEB2B2',
  },
  errorText: {
    fontSize: 16,
    color: '#E53E3E',
    textAlign: 'center',
    marginBottom: 12,
  },
  retryButton: {
    backgroundColor: '#E53E3E',
    padding: 10,
    borderRadius: 6,
    alignSelf: 'center',
  },
  retryButtonText: {
    color: Colors.background,
    fontSize: 14,
    fontWeight: '600',
  },
  eventCard: {
    marginHorizontal: 0,
  },
  attachmentInfo: {
    fontSize: 14,
    color: Colors.green,
    fontStyle: 'italic',
    textAlign: 'center',
    padding: 10,
    backgroundColor: '#F8F9FA',
    borderRadius: 6,
    borderWidth: 1,
    borderColor: '#E9ECEF',
    marginTop: 8,
  },
  loadingOverlay: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    backgroundColor: 'rgba(255,255,255,0.7)',
    zIndex: 10,
    justifyContent: 'center',
    alignItems: 'center',
  },
  submitButton: {
    backgroundColor: Colors.primary,
    paddingVertical: 12,
    paddingHorizontal: 70,
    borderRadius: 30,
    alignSelf: 'center',
    marginTop: 8,
    margin: 20,
    alignItems: 'center',
    justifyContent: 'center',
  },
  disabledButton: {
    backgroundColor: Colors.disabled,
  },
  buttonText: {
    color: Colors.background,
    fontSize: 14,
    fontWeight: '500',
    fontFamily: 'Roboto',
  },
})

export default HearingEvents