import React, { useState, useEffect } from "react";
import { View, Text, TouchableOpacity, Image, StyleSheet, TextInput, ScrollView } from "react-native";
import { useRouter } from 'expo-router';
import Constants from 'expo-constants';
import { useAuth } from '../../../../context/auth-context';
import FontAwesome from "@expo/vector-icons/FontAwesome";
import { apiService } from '../../../../services/api';

export default function HomeDispatcher() {
  const router = useRouter();
  const { token } = useAuth();
  const [searchQuery, setSearchQuery] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const [events, setEvents] = useState([]);

  useEffect(() => {
    const fetchEvents = async () => {
      if (!token) return;

      try {
        setIsLoading(true);
        // console.log('=== FETCHING EVENTS ===');
        // console.log('Token available:', !!token);

        const response = await apiService.fetchProsecutorEvents(token);
        // console.log('Events API Response:', JSON.stringify(response, null, 2));

        if (response.status === 'success' || response.data) {
          const eventsData = response.data || [];
          // console.log('✅ Events fetched successfully:', eventsData.length, 'events');

          // Transform API data to calendar format
          const transformedEvents = eventsData.map(event => ({
            title: event.title,
            start: new Date(event.startDateTime),
            end: new Date(event.endDateTime),
            color: event.eventColor || '#0B36A1',
            details: event.description,
            id: event._id,
            caseId: event.caseId,
            duration: event.duration,
            attachmentUrl: event.attachmentUrl,
            assignedJudge: event.assignedJudge,
            assignedProsecutor: event.assignedProsecutor,
            assignedCourt: event.assignedCourt
          }));

          setEvents(transformedEvents);
          // console.log('Events set in calendar:', transformedEvents.length);
        } else {
          console.log('No events found or API error:', response.message);
          setEvents([]);
        }
      } catch (error) {
        console.error('Error fetching events:', error.message);
        setEvents([]);
      } finally {
        setIsLoading(false);
      }
    };

    fetchEvents();
  }, [token]);

  const handleSearch = () => {
    if (!searchQuery.trim()) return;
    router.push('(prosecutor)/(screensDispatchers)/prosecutorRecentCases');
  };

  return (
    <View style={styles.mainContainer}>
      <View style={styles.contentContainer}>
        <View style={styles.searchContainer}>
          <View style={styles.searchBar}>
            <FontAwesome name="search" size={20} color="#666" />
            <TextInput
              style={styles.searchInput}
              placeholder="Search packages..."
              value={searchQuery}
              onChangeText={setSearchQuery}
              onSubmitEditing={handleSearch}
            />
            {searchQuery !== '' && (
              <TouchableOpacity onPress={() => setSearchQuery('')}>
                <FontAwesome name="times-circle" size={20} color="#666" />
              </TouchableOpacity>
            )}
          </View>
          <TouchableOpacity 
            style={[styles.searchButton, isLoading && styles.searchButtonDisabled]}
            onPress={handleSearch}
            disabled={isLoading}
          >
            <Text style={styles.searchButtonText}>
              {isLoading ? 'Searching...' : 'Search'}
            </Text>
          </TouchableOpacity>
        </View>

        <View style={styles.container}>
          <View style={styles.buttonContainer}>
            <TouchableOpacity 
              style={styles.caseButton} 
              onPress={() => router.push('(prosecutor)/(screensDispatchers)/prosecutorRecentCases')}
            >
              <Image 
                source={require('../../../../assets/images/recent_cases.png')} 
                style={styles.buttonIcon} 
              />
              <Text style={styles.buttonText}>Recent Cases</Text>
            </TouchableOpacity>
          </View>
          
          {/* Upcoming Events Section */}
          <View style={styles.eventsOuterContainer}>
            <Text style={styles.eventsTitle}>UPCOMING EVENTS</Text>
            
            <ScrollView style={styles.eventsScrollView} showsVerticalScrollIndicator={false}>
              {events.length > 0 ? (
                // Group events by month
                Object.entries(
                  events.reduce((acc, event) => {
                    const date = new Date(event.start);
                    const monthYear = date.toLocaleString('default', { month: 'long', year: 'numeric' }).toUpperCase();
                    if (!acc[monthYear]) {
                      acc[monthYear] = [];
                    }
                    acc[monthYear].push(event);
                    return acc;
                  }, {})
                ).map(([monthYear, monthEvents]) => (
                  <View key={monthYear}>
                    <Text style={styles.monthTitle}>{monthYear}</Text>
                    {monthEvents.map((event, index) => (
                      <TouchableOpacity 
                        key={`${event.id}-${index}`} 
                        style={styles.eventCard}
                        onPress={() => router.push({
                          pathname: '/(prosecutor)/(screensDispatchers)/videoStatement',
                          params: {
                            eventDetails: JSON.stringify({
                              eventId: event.id,
                              title: event.title,
                              startDateTime: event.start,
                              endDateTime: event.end,
                              description: event.details,
                              caseId: event.caseId,
                              duration: event.duration,
                              attachmentUrl: event.attachmentUrl,
                              assignedJudge: event.assignedJudge,
                              assignedProsecutor: event.assignedProsecutor,
                              assignedCourt: event.assignedCourt
                            })
                          }
                        })}
                      >
                        <View style={styles.dateColumn}>
                          <Text style={styles.dateNumber}>
                            {new Date(event.start).getDate()}
                          </Text>
                          <Text style={styles.dateDay}>
                            {new Date(event.start).toLocaleString('default', { weekday: 'short' })}
                          </Text>
                        </View>
                        <View style={styles.blueBar}></View>
                        <View style={styles.eventsColumn}>
                          <View style={styles.eventItem}>
                            <Text style={styles.eventName}>{event.title}</Text>
                            <Text style={styles.eventTime}>
                              {new Date(event.start).toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' })}
                            </Text>
                          </View>
                        </View>
                      </TouchableOpacity>
                    ))}
                  </View>
                ))
              ) : (
                <View style={styles.noEventsContainer}>
                  <Text style={styles.noEventsText}>No upcoming events</Text>
                </View>
              )}
            </ScrollView>
          </View>
        </View>
      </View>
    </View>
  );
}

const styles = StyleSheet.create({
  mainContainer: {
    flex: 1,
    backgroundColor: "#fff",
  },
  contentContainer: {
    flex: 1,
  },
  container: {
    flex: 1,
    alignItems: "center",
    backgroundColor: "#fff",
    padding: 30,
  },
  searchContainer: {
    width: '100%',
    flexDirection: 'row',
    alignItems: 'center',
    gap: 10,
    padding: 20,
    backgroundColor: '#fff',
  },
  searchBar: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#F5F5F5',
    borderRadius: 8,
    paddingHorizontal: 12,
  },
  searchInput: {
    flex: 1,
    paddingVertical: 12,
    paddingHorizontal: 8,
    fontSize: 16,
    fontFamily: 'Roboto',
  },
  searchButton: {
    backgroundColor: '#0B36A1',
    paddingHorizontal: 20,
    paddingVertical: 12,
    borderRadius: 8,
  },
  searchButtonText: {
    color: '#FFFFFF',
    fontFamily: 'Roboto',
    fontSize: 16,
  },
  searchButtonDisabled: {
    backgroundColor: '#cccccc',
  },
  buttonContainer: {
    flexDirection: "row",
    justifyContent: "center",
    gap: 30,
    width: "100%",
    marginBottom: 30,
  },
  caseButton: {
    width: 140,
    height: 140,
    alignItems: "center",
    justifyContent: "center",
    borderWidth: 3,
    borderColor: "#d0d9f5",
    borderRadius: 16,
    borderStyle: "dashed",
    backgroundColor: "#fff",
  },
  buttonIcon: {
    width: 40,
    height: 40,
    resizeMode: "contain",
    tintColor: "#0B36A1",
  },
  buttonText: {
    marginTop: 10,
    fontSize: 18,
    fontWeight: "bold",
    color: "#0B36A1",
    textAlign: "center",
    fontFamily: "Roboto_bold",
  },
  // Upcoming Events Styles
  eventsOuterContainer: {
    flex: 1,
    width: '100%',
    marginTop: 20,
  },
  eventsScrollView: {
    flex: 1,
  },
  eventsTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#333',
    marginBottom: 20,
  },
  monthTitle: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#333',
    textAlign: 'center',
    marginVertical: 15,
  },
  eventCard: {
    width: '100%',
    flexDirection: 'row',
    backgroundColor: '#fff',
    borderRadius: 8,
    marginBottom: 15,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
    elevation: 2,
  },
  dateColumn: {
    width: 50,
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 10,
  },
  dateNumber: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#333',
  },
  dateDay: {
    fontSize: 14,
    color: '#666',
  },
  blueBar: {
    width: 5,
    backgroundColor: '#0B36A1',
  },
  eventsColumn: {
    flex: 1,
    paddingVertical: 5,
  },
  eventItem: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingVertical: 12,
    paddingHorizontal: 15,
    backgroundColor: '#f8f9fc',
    marginVertical: 5,
    marginHorizontal: 10,
    borderRadius: 4,
  },
  eventName: {
    fontSize: 16,
    color: '#333',
  },
  eventTime: {
    fontSize: 14,
    color: '#666',
  },
  noEventsContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  noEventsText: {
    fontSize: 16,
    color: '#666',
  },
});