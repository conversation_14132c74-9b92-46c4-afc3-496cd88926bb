import React, { useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
} from 'react-native';
import { GestureHandlerRootView } from 'react-native-gesture-handler';
import { router } from 'expo-router';
import CapturePageHeader from '../../../../components/Smalls/CapturePageHeader';
import EventsList from '../../../../components/Larges/EventsList';
import { Colors } from '../../../../constants/colors';

const Tab3 = ({ caseid }) => {
  const [refreshTrigger, setRefreshTrigger] = useState(0);

  // Handle event press - navigate to event details
  const handleEventPress = (event) => {
    router.push({
      pathname: '/(prosecutor)/(screensDispatchers)/videoStatement',
      params: {
        eventDetails: JSON.stringify({
          title: event.title,
          details: event.description,
          start: event.startDateTime,
          end: event.endDateTime,
          color: event.eventColor,
          id: event._id,
          caseId: event.caseId,
          duration: event.duration,
          attachmentUrl: event.attachmentUrl,
          isUpdate: true,
          eventId: event._id
        })
      }
    });
  };

  // Handle schedule hearing button press
  const handleScheduleHearing = () => {
    router.push({
      pathname: '/(prosecutor)/(screensDispatchers)/scheduleHearingProsecutor',
      params: {
        caseId: caseid,
      },
    });
  };

  return (
    <GestureHandlerRootView style={{ flex: 1 }}>
      <View style={styles.container}>
        <ScrollView style={styles.content} contentContainerStyle={{ paddingBottom: 10 }}>
        

          {/* Events List */}
          <EventsList
            caseId={caseid}
            title="Court Proceedings"
            onEventPress={handleEventPress}
            showAttachments={true}
            refreshTrigger={refreshTrigger}
          />

          {/* Schedule Hearing Button */}
          <View style={styles.scheduleHearingContainer}>
            <TouchableOpacity
              style={styles.scheduleHearingButton}
              onPress={handleScheduleHearing}
            >
              <Text style={styles.scheduleHearingButtonText}>SCHEDULE HEARING</Text>
            </TouchableOpacity>
          </View>
        </ScrollView>
      </View>
    </GestureHandlerRootView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#fff',
  },
  content: {
    flex: 1,
  },
  scheduleHearingContainer: {
    paddingHorizontal: 20,
    paddingVertical: 20,
    marginBottom: 20,
    alignItems: 'center',
  },
  scheduleHearingButton: {
    backgroundColor: Colors.primary,
    paddingVertical: 16,
    paddingHorizontal: 32,
    borderRadius: 50,
    alignItems: 'center',
    justifyContent: 'center',
    minHeight: 50,
    width: '100%',
    maxWidth: 300,
  },
  scheduleHearingButtonText: {
    color: Colors.background,
    fontSize: 16,
    fontWeight: '500',
    fontFamily: 'Roboto_medium',
  },
});

export default Tab3; 