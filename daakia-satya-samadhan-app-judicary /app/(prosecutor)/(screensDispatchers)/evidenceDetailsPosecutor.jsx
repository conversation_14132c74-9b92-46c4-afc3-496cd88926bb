import {
    View,
    Text,
    TouchableOpacity,
    ScrollView,
    Image,
    StyleSheet,
    FlatList,
    ActivityIndicator,
    useWindowDimensions,
    Modal,
  } from 'react-native';
  import React, { useEffect, useState } from 'react';
  import { useLocalSearchParams } from 'expo-router';
  import Constants from 'expo-constants';
  import { useAuth } from '../../../context/auth-context';
  import VideoPlayer from '../../../components/Larges/VideoPlayer';
  import PreviewComponent from '../../../components/Larges/PreviewComponent';
  import { transformUrl } from '../../../utils/transformUrl';
  import { apiService } from '../../../services/api';
  
  // Utility functions
  const isVideoFile = (url) => {
    if (!url) return false;
    const lowerCaseUrl = url.toLowerCase();
    return lowerCaseUrl.endsWith('.mp4') || 
           lowerCaseUrl.endsWith('.mov') || 
           lowerCaseUrl.endsWith('.avi') || 
           lowerCaseUrl.endsWith('.mkv');
  };
  
  const formatDate = (dateString) => {
    return new Date(dateString).toLocaleString();
  };
  
  export default function EvidenceDetailsPosecutor() {
    const { evidenceId, caseid } = useLocalSearchParams();
    const [evidenceData, setEvidenceData] = useState(null);
    const [isLoading, setIsLoading] = useState(true);
    const [error, setError] = useState(null);
    const { width } = useWindowDimensions();
    const { token } = useAuth();
    const [currentIndex, setCurrentIndex] = useState(0);
    const [previewUri, setPreviewUri] = useState(null);
  
    const BASE_URL = Constants.expoConfig.extra.baseUrl;
  
    useEffect(() => {
      fetchEvidenceDetails();
    }, [caseid, evidenceId]);
  
    const fetchEvidenceDetails = async () => {
      if (!caseid || !evidenceId) {
        console.log('Missing required info:', { caseid, evidenceId });
        setError('Missing required information');
        setIsLoading(false);
        return;
      }
    
      try {
        const result = await apiService.fetchEvidenceDetails(token, caseid, evidenceId);
        setEvidenceData(result.data);
        setError(null);
      } catch (error) {
        console.error('Error fetching evidence details:', error);
        setError('Failed to load evidence details. Please try again.');
      } finally {
        setIsLoading(false);
      }
    };
  
    if (isLoading) {
      return (
        <View style={styles.centerContainer}>
          <ActivityIndicator size="large" color="#0B36A1" />
        </View>
      );
    }
  
    if (error) {
      return (
        <View style={styles.centerContainer}>
          <Text style={styles.errorText}>{error}</Text>
          <TouchableOpacity onPress={fetchEvidenceDetails} style={styles.retryButton}>
            <Text style={styles.retryButtonText}>Retry</Text>
          </TouchableOpacity>
        </View>
      );
    }
  
    if (!evidenceData) {
      return (
        <View style={styles.centerContainer}>
          <Text>No evidence data found</Text>
        </View>
      );
    }
  
    const hasLabInfo = evidenceData.labId || evidenceData.labDepartmentId;
  
    return (
      <ScrollView style={styles.container}>
        {/* Evidence Photos/Videos Section */}
        <View style={styles.sectionContainer}>
          <Text style={styles.sectionLabel}>Evidence Media</Text>
          {evidenceData.attachmentUrl && evidenceData.attachmentUrl.length > 0 ? (
            <>
              <FlatList
                data={evidenceData.attachmentUrl}
                renderItem={({ item }) => {
                  const mediaUrl = transformUrl(item);
                  const videoStyles = { width: width - 32, height: 240 };
  
                  return (
                    <TouchableOpacity
                      onPress={() => setPreviewUri(mediaUrl)}
                    >
                      {isVideoFile(item) ? (
                        <VideoPlayer uri={mediaUrl} style={videoStyles} />
                      ) : (
                        <Image
                          source={{ uri: mediaUrl }}
                          style={[styles.evidenceMedia, { width: width - 32 }]}
                          resizeMode="cover"
                        />
                      )}
                    </TouchableOpacity>
                  );
                }}
                keyExtractor={(_, index) => `evidence-${index}`}
                horizontal
                pagingEnabled
                showsHorizontalScrollIndicator={false}
                snapToInterval={width - 32}
                decelerationRate="fast"
                onScroll={(event) => {
                  const offset = event.nativeEvent.contentOffset.x;
                  const index = Math.round(offset / (width - 32));
                  setCurrentIndex(index);
                }}
                contentContainerStyle={styles.mediaListContainer}
              />
  
              <View style={styles.swipeIndicatorContainer}>
                {evidenceData.attachmentUrl.map((_, index) => (
                  <View
                    key={index}
                    style={[
                      styles.swipeIndicator,
                      index === currentIndex && styles.activeSwipeIndicator,
                    ]}
                  />
                ))}
              </View>
            </>
          ) : (
            <Text style={styles.noDataText}>No evidence media available</Text>
          )}
        </View>
  
        {/* Evidence Details Section */}
        <View style={styles.sectionContainer}>
          <Text style={styles.sectionLabel}>Evidence Information</Text>
          <View style={styles.detailsCard}>
            {evidenceData.type && (
              <View style={styles.detailRow}>
                <Text style={styles.detailLabel}>Type:</Text>
                <Text style={styles.detailValue}>{evidenceData.type}</Text>
              </View>
            )}
  
            {evidenceData.title && (
              <View style={styles.detailRow}>
                <Text style={styles.detailLabel}>Title:</Text>
                <Text style={styles.detailValue}>{evidenceData.title}</Text>
              </View>
            )}
  
            {evidenceData.description && (
              <View style={styles.detailRow}>
                <Text style={styles.detailLabel}>Description:</Text>
                <Text style={styles.detailValue}>{evidenceData.description}</Text>
              </View>
            )}
  
            {evidenceData.time && (
              <View style={styles.detailRow}>
                <Text style={styles.detailLabel}>Date & Time:</Text>
                <Text style={styles.detailValue}>{formatDate(evidenceData.time)}</Text>
              </View>
            )}
  
            {evidenceData.gpsLocation && (
              <View style={styles.detailRow}>
                <Text style={styles.detailLabel}>Location:</Text>
                <Text style={styles.detailValue}>{evidenceData.gpsLocation}</Text>
              </View>
            )}
          </View>
        </View>
  
        {/* Lab Information Section - Only shown if lab info exists */}
        {hasLabInfo && (
          <View style={styles.sectionContainer}>
            <Text style={styles.sectionLabel}>Forensic Laboratory Details</Text>
            <View style={styles.detailsCard}>
              {evidenceData.labId && (
                <View style={styles.detailRow}>
                  <Text style={styles.detailLabel}>Laboratory:</Text>
                  <Text style={styles.detailValue}>{evidenceData.labId.name}</Text>
                </View>
              )}
  
              {evidenceData.labDepartmentId && (
                <View style={styles.detailRow}>
                  <Text style={styles.detailLabel}>Department:</Text>
                  <Text style={styles.detailValue}>{evidenceData.labDepartmentId.name}</Text>
                </View>
              )}
            </View>
          </View>
        )}
  
        {/* Preview Modal */}
        <Modal
          visible={!!previewUri}
          transparent={true}
          animationType="slide"
          onRequestClose={() => setPreviewUri(null)}
        >
          <PreviewComponent
            uri={previewUri}
            onClose={() => setPreviewUri(null)}
          />
        </Modal>
      </ScrollView>
    );
  }
  
  const styles = StyleSheet.create({
    container: {
      flex: 1,
      backgroundColor: '#F5F7FA',
    },
    centerContainer: {
      flex: 1,
      justifyContent: 'center',
      alignItems: 'center',
      backgroundColor: '#F5F7FA',
      padding: 16,
    },
    sectionContainer: {
      marginBottom: 20,
      paddingTop: 12,
      fontFamily: 'Roboto',
    },
    mediaListContainer: {
      paddingHorizontal: 16,
    },
    sectionLabel: {
      fontSize: 18,
      fontWeight: '600',
      color: '#0B36A1',
      marginBottom: 12,
      paddingHorizontal: 16,
      fontFamily: 'Roboto_bold',
    },
    evidenceMedia: {
      height: 240,
      borderRadius: 8,
      marginRight: 16,
    },
    noDataText: {
      fontSize: 14,
      color: '#888',
      fontStyle: 'italic',
      textAlign: 'center',
      padding: 16,
      fontFamily: 'Roboto',
    },
    errorText: {
      color: '#EB5757',
      marginBottom: 16,
      textAlign: 'center',
      fontFamily: 'Roboto',
    },
    retryButton: {
      backgroundColor: '#0B36A1',
      paddingVertical: 12,
      paddingHorizontal: 24,
      borderRadius: 8,
      fontFamily: 'Roboto',
    },
    retryButtonText: {
      color: '#FFFFFF',
      fontWeight: '500',
      fontFamily: 'Roboto',
    },
    detailsCard: {
      backgroundColor: 'white',
      borderRadius: 12,
      padding: 16,
      marginHorizontal: 16,
      shadowColor: '#000',
      shadowOffset: { width: 0, height: 2 },
      shadowOpacity: 0.1,
      shadowRadius: 4,
      elevation: 3,
      fontFamily: 'Roboto',
    },
    detailRow: {
      flexDirection: 'row',
      paddingVertical: 10,
      borderBottomWidth: 1,
      borderBottomColor: '#F0F0F0',
      fontFamily: 'Roboto',
    },
    detailLabel: {
      width: '40%',
      fontSize: 14,
      fontWeight: '500',
      color: '#555',
      fontFamily: 'Roboto',
    },
    detailValue: {
      flex: 1,
      fontSize: 14,
      color: '#333',
      fontFamily: 'Roboto',
    },
    swipeIndicatorContainer: {
      flexDirection: 'row',
      justifyContent: 'center',
      alignItems: 'center',
      marginTop: 12,
    },
    swipeIndicator: {
      width: 8,
      height: 8,
      borderRadius: 4,
      backgroundColor: '#ccc',
      marginHorizontal: 4,
    },
    activeSwipeIndicator: {
      backgroundColor: '#0B36A1',
      width: 10,
      height: 10,
      borderRadius: 5,
  
    },
  });