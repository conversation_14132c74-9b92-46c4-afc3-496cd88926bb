import { 
  View, 
  Text, 
  useWindowDimensions,
  TouchableOpacity,
  FlatList,
  ActivityIndicator,
  Platform,
  RefreshControl,
  TextInput,
  Image,
} from "react-native";
import FontAwesome from "@expo/vector-icons/FontAwesome";
import { useEffect, useState, useCallback, useMemo } from "react";
import { router } from "expo-router";
import Constants from 'expo-constants'; 
import { useAuth } from "../../../context/auth-context";
import { apiService } from "../../../services/api";

const BASE_URL = Constants.expoConfig.extra.baseUrl;

// Utility functions
const formatDate = (dateString) => {
  try {
    const date = new Date(dateString);
    if (isNaN(date.getTime())) throw new Error('Invalid date');
    return date.toLocaleDateString('en-GB');
  } catch (e) {
    console.error('Date formatting error:', e);
    return 'Invalid Date';
  }
};

const formatTime = (timeString) => {
  try {
    const date = new Date(timeString);
    if (isNaN(date.getTime())) throw new Error('Invalid time');
    const hours = String(date.getUTCHours()).padStart(2, "0");
    const minutes = String(date.getUTCMinutes()).padStart(2, "0");
    return `${hours}:${minutes}`;
  } catch (e) {
    console.error('Time formatting error:', e);
    return 'Invalid Time';
  }
};

// Component for search bar
const SearchBar = ({ width, height, searchQuery, setSearchQuery, showFilters, setShowFilters }) => (
  <View style={{ 
    marginHorizontal: width * 0.05,
    marginTop: height * 0.02,
    marginBottom: height * 0.01,
    flexDirection: 'row',
    alignItems: 'center',
    gap: 10,
  }}>
    <View style={{
      flex: 1,
      flexDirection: 'row',
      alignItems: 'center',
      backgroundColor: '#F5F5F5',
      borderRadius: 8,
      paddingHorizontal: 12,
    }}>
      <FontAwesome name="search" size={20} color="#666" />
      <TextInput
        style={{
          flex: 1,
          paddingVertical: 12,
          paddingHorizontal: 8,
          fontSize: 16,
          fontFamily: 'Roboto',
        }}
        placeholder="Search cases..."
        value={searchQuery}
        onChangeText={setSearchQuery}
      />
      {searchQuery !== '' && (
        <TouchableOpacity onPress={() => setSearchQuery('')}>
          <FontAwesome name="times-circle" size={20} color="#666" />
        </TouchableOpacity>
      )}
    </View>

    <TouchableOpacity 
      onPress={() => setShowFilters(!showFilters)}
      style={{
        flexDirection: 'row',
        alignItems: 'center',
        padding: 12,
        backgroundColor: showFilters ? '#0B36A1' : '#F5F5F5',
        borderRadius: 8,
        gap: 6,
      }}
    >
      <Text style={{ 
        color: showFilters ? '#FFFFFF' : '#666',
        fontFamily: 'Roboto',
        fontSize: 16,
      }}>
        Filter
      </Text>
      
      <Image 
        source={require('../../../assets/images/Filter.png')}
        style={{
          width: 20,
          height: 20,
          tintColor: showFilters ? '#FFFFFF' : '#666'
        }}
        resizeMode="contain"
      />
    </TouchableOpacity>
  </View>
);

// Keep FilterBar as a separate component but show/hide based on showFilters state
const FilterBar = ({ width, height, sortOption, setSortOption, visible }) => (
  visible ? (
    <View style={{ 
      marginHorizontal: width * 0.05,
      marginTop: height * 0.01,
      marginBottom: height * 0.01,
      flexDirection: 'row',
      gap: 10,
    }}>
      <TouchableOpacity
        style={{
          backgroundColor: sortOption === 'az' ? '#0B36A1' : '#F5F5F5',
          paddingHorizontal: 12,
          paddingVertical: 8,
          borderRadius: 8,
          flexDirection: 'row',
          alignItems: 'center',
          gap: 5,
        }}
        onPress={() => setSortOption('az')}
      >
        <FontAwesome name="sort-alpha-asc" size={16} color={sortOption === 'az' ? '#FFFFFF' : '#666'} />
        <Text style={{ 
          color: sortOption === 'az' ? '#FFFFFF' : '#666',
          fontFamily: 'Roboto'
        }}>A-Z</Text>
      </TouchableOpacity>

      <TouchableOpacity
        style={{
          backgroundColor: sortOption === 'newest' ? '#0B36A1' : '#F5F5F5',
          paddingHorizontal: 12,
          paddingVertical: 8,
          borderRadius: 8,
          flexDirection: 'row',
          alignItems: 'center',
          gap: 5,
        }}
        onPress={() => setSortOption('newest')}
      >
        <FontAwesome name="sort-amount-desc" size={16} color={sortOption === 'newest' ? '#FFFFFF' : '#666'} />
        <Text style={{ 
          color: sortOption === 'newest' ? '#FFFFFF' : '#666',
          fontFamily: 'Roboto'
        }}>Newest</Text>
      </TouchableOpacity>

      <TouchableOpacity
        style={{
          backgroundColor: sortOption === 'oldest' ? '#0B36A1' : '#F5F5F5',
          paddingHorizontal: 12,
          paddingVertical: 8,
          borderRadius: 8,
          flexDirection: 'row',
          alignItems: 'center',
          gap: 5,
        }}
        onPress={() => setSortOption('oldest')}
      >
        <FontAwesome name="sort-amount-asc" size={16} color={sortOption === 'oldest' ? '#FFFFFF' : '#666'} />
        <Text style={{ 
          color: sortOption === 'oldest' ? '#FFFFFF' : '#666',
          fontFamily: 'Roboto'
        }}>Oldest</Text>
      </TouchableOpacity>
    </View>
  ) : null
);

const ColumnHeaders = ({ width, height }) => (
  <View
    style={{
      flexDirection: "row",
      justifyContent: "space-between",
      marginTop: height * 0.01,
      marginHorizontal: width * 0.05,
      marginBottom: height * 0.01,
    }}
  >
    <Text style={{ opacity: 0.5, fontFamily: 'Roboto' }}>Case Name</Text>
    <View
      style={{
        flexDirection: "row",
        justifyContent: "space-around",
        width: width * 0.4,
      }}
    >
      <Text style={{ opacity: 0.5, fontFamily: 'Roboto' }}>Time</Text>
      <Text style={{ opacity: 0.5, fontFamily: 'Roboto' }}>Date</Text>
    </View>
  </View>
);

const CaseListItem = ({ item, width, onPress }) => (
  <TouchableOpacity
    style={{
      flexDirection: "row",
      justifyContent: "space-between",
      paddingHorizontal: width * 0.05,
      paddingVertical: 15,
      borderTopWidth: 1,
      borderColor: "#BFC7D2",
      alignItems: "center",
    }}
    onPress={onPress}
  >
    <View style={{ flexDirection: "row", alignItems: "center", gap: 10 }}>
      <FontAwesome name="folder" size={24} color="#0B36A1" />
      <Text style={{ fontSize: 16, fontFamily: 'Roboto' }}>{item.CaseName}</Text>
    </View>
    <View
      style={{
        flexDirection: "row",
        width: width * 0.4,
        justifyContent: "space-between",
        alignItems: "center",
      }}
    >
      <Text style={{ width: width * 0.2, textAlign: "center", fontFamily: 'Roboto' }}>
        {item.time}
      </Text>
      <Text style={{ fontFamily: 'Roboto' }}>{item.date}</Text>
    </View>
  </TouchableOpacity>
);

export default function ProsecutorRecentCases() {
  const [cases, setCases] = useState([]);
  const [searchQuery, setSearchQuery] = useState('');
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState(null);
  const [refreshing, setRefreshing] = useState(false);
  const [currentPage, setCurrentPage] = useState(1);
  const [totalPages, setTotalPages] = useState(1);
  const { width, height } = useWindowDimensions();
  const { token } = useAuth();
  const [sortOption, setSortOption] = useState('newest');
  const [allCases, setAllCases] = useState([]);
  const [isLoadingMore, setIsLoadingMore] = useState(false);
  const [showFilters, setShowFilters] = useState(false);

  const filteredCases = useMemo(() => {
    let filtered = allCases.filter(item => {
      if (!item || typeof item.CaseName !== 'string') return false;
      return item.CaseName.toLowerCase().includes(searchQuery.toLowerCase());
    });

    // Apply sorting
    return filtered.sort((a, b) => {
      switch (sortOption) {
        case 'az':
          return a.CaseName.localeCompare(b.CaseName);
        case 'newest':
          return b.timestamp - a.timestamp;
        case 'oldest':
          return a.timestamp - b.timestamp;
        default:
          return 0;
      }
    });
  }, [allCases, searchQuery, sortOption]);

  const fetchAllCases = useCallback(async () => {
    try {
      setIsLoading(true);
      const result = await apiService.fetchProsecutorCases(token, currentPage, 50);

      if (!result.data || !Array.isArray(result.data.cases)) {
        throw new Error('Invalid data format received');
      }

      const formattedCases = result.data.cases.map(item => ({
        id: item._id,
        CaseName: item.title || 'Untitled Case',
        time: formatTime(item.createdAt),
        date: formatDate(item.createdAt),
        timestamp: new Date(item.createdAt).getTime(),
        caseData: item // Store the full case data for navigation
      }));

      setAllCases(formattedCases);
      setTotalPages(result.data.totalPages || 1);
      setError(null);
    } catch (err) {
      console.error('Failed to fetch cases:', err);
      setError('Failed to load cases. Please try again.');
    } finally {
      setIsLoading(false);
    }
  }, [token, currentPage]);

  const loadMoreCases = useCallback(async () => {
    if (currentPage >= totalPages) return;
    
    try {
      const nextPage = currentPage + 1;
      const result = await apiService.fetchProsecutorCases(token, nextPage, 50);
      
      const formattedCases = result.data.cases.map(item => ({
        id: item._id,
        CaseName: item.title || 'Untitled Case',
        time: formatTime(item.createdAt),
        date: formatDate(item.createdAt),
        timestamp: new Date(item.createdAt).getTime(),
        caseData: item
      }));

      setAllCases(prev => [...prev, ...formattedCases]);
      setCurrentPage(nextPage);
    } catch (err) {
      console.error('Failed to load more cases:', err);
    }
  }, [currentPage, totalPages, token]);

  useEffect(() => {
    fetchAllCases();
  }, [fetchAllCases]);

  const onRefresh = useCallback(() => {
    setRefreshing(true);
    fetchAllCases().finally(() => setRefreshing(false));
  }, [fetchAllCases]);

  const navigateToCase = useCallback((caseItem) => {
    router.push({
      pathname: '/(screensDispatchers)/caseDetails',
      params: { caseid: caseItem.id },
    });
  }, []);

  const segregateEvidences = (caseItem) => {
    const forensicRequestId = caseItem._id;
    const segregatedEvidences = {};
    
    caseItem.evidences.forEach(evidence => {
      const labId = evidence.labId._id;
      const labDepartmentId = evidence.labDepartmentId._id;
      
      if (!segregatedEvidences[labId]) {
        segregatedEvidences[labId] = {
          name: evidence.labId.name,
          departments: {}
        };
      }
      
      if (!segregatedEvidences[labId].departments[labDepartmentId]) {
        segregatedEvidences[labId].departments[labDepartmentId] = {
          name: evidence.labDepartmentId.name,
          evidences: []
        };
      }
      
      segregatedEvidences[labId].departments[labDepartmentId].evidences.push({
        ...evidence,
        status: caseItem.status,
        forensicRequestId
      });
    });
    
    return segregatedEvidences;
  };

  if (isLoading) {
    return (
      <View style={{ flex: 1, justifyContent: 'center', alignItems: 'center', backgroundColor: '#FFFFFF' }}>
        <ActivityIndicator size="large" color="#0B36A1" />
        </View>
      );
    }
    
    if (error) {
      return (
      <View style={{ flex: 1, justifyContent: 'center', alignItems: 'center', backgroundColor: '#FFFFFF', padding: 20 }}>
        <Text style={{ color: 'red', textAlign: 'center', marginBottom: 20, fontFamily: 'Roboto' }}>{error}</Text>
        <TouchableOpacity 
          onPress={() => fetchAllCases()}
          style={{ 
            backgroundColor: '#0B36A1', 
            padding: 10, 
            borderRadius: 5 
          }}
        >
          <Text style={{ color: 'white', fontFamily: 'Roboto' }}>Retry</Text>
          </TouchableOpacity>
        </View>
      );
    }
    
      return (
    <View style={{ flex: 1, backgroundColor: "#FFFFFF" }}>
      <SearchBar
        width={width}
        height={height}
        searchQuery={searchQuery}
        setSearchQuery={setSearchQuery}
        showFilters={showFilters}
        setShowFilters={setShowFilters}
      />
      <FilterBar
        width={width}
        height={height}
        sortOption={sortOption}
        setSortOption={setSortOption}
        visible={showFilters}
      />
      
      <ColumnHeaders width={width} height={height} />

      <View style={{ flex: 1 }}>
        <FlatList
          data={filteredCases}
          renderItem={({ item }) => (
            <CaseListItem 
              item={item} 
              width={width} 
              onPress={() => navigateToCase(item)}
            />
          )}
          keyExtractor={item => item.id}
          refreshControl={
            <RefreshControl
              refreshing={refreshing}
              onRefresh={onRefresh}
              colors={["#0B36A1"]}
              tintColor="#0B36A1"
            />
          }
          onEndReached={loadMoreCases}
          onEndReachedThreshold={0.5}
          ListEmptyComponent={
            <View style={{ flex: 1, justifyContent: 'center', alignItems: 'center', marginTop: 50 }}>
              <Text style={{ fontFamily: 'Roboto' }}>{searchQuery ? 'No matching cases found' : 'No cases found'}</Text>
        </View>
          }
          ListFooterComponent={
            currentPage < totalPages ? (
              <View style={{ padding: 20 }}>
                <ActivityIndicator color="#0B36A1" />
        </View>
            ) : (
              <View style={{ height: 50 }} />
            )
          }
        />
      </View>
    </View>
  );
}