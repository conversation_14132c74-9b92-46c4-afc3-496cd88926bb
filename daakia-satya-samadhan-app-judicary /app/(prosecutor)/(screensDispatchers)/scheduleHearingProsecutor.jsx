import React, { useState, useRef, useCallback, useMemo, useEffect } from 'react';
import {
  View,
  Text,
  TouchableOpacity,
  ScrollView,
  Animated,
  Platform,
  Modal,
  StatusBar,
  Alert,
  ActivityIndicator
} from 'react-native';
import { Calendar } from 'react-native-big-calendar';
import { Ionicons } from '@expo/vector-icons';
import { useSafeAreaInsets } from 'react-native-safe-area-context';
import { Colors } from '../../../constants/colors';
import { styles, DRAWER_WIDTH, IS_TABLET, SCREEN_WIDTH, SCREEN_HEIGHT } from '../../../components/DispatcherComponets/CalendarCompoProsecutor/styles/scheduleHearing.styles';
import MiniCalendar from '../../../components/DispatcherComponets/CalendarCompoProsecutor/MiniCalendar';
import AgendaView from '../../../components/DispatcherComponets/CalendarCompoProsecutor/AgendaView';
import CreateEventModal from '../../../components/DispatcherComponets/CalendarCompoProsecutor/CreateEventModal';
import { useLocalSearchParams, useRouter } from 'expo-router';
import { apiService } from '../../../services/api';
import useUploadMedia from '../../../hooks/useUploadMedia';
import { useAuth } from '../../../context/auth-context';
import CaseSelectionModal from '../../../components/DispatcherComponets/CalendarCompoProsecutor/CaseSelectionModal';

const ScheduleHearing = () => {
  const params = useLocalSearchParams();
  const router = useRouter();
  const { uploadMedia, isUploading } = useUploadMedia();
  const { token } = useAuth();
console.log(token)
  // Get caseId from params (can be either caseId or caseid)
  const caseId = params.caseId || params.caseid;

  const [selectedDate, setSelectedDate] = useState(new Date());
  const [viewMode, setViewMode] = useState('week');
  const [isDrawerOpen, setIsDrawerOpen] = useState(false);
  const [showViewSelector, setShowViewSelector] = useState(false);
  const [viewYear, setViewYear] = useState(new Date().getFullYear());
  const insets = useSafeAreaInsets();

  // Ref for the view selector button to position the dropdown
  const weekButtonRef = useRef(null);
  const [dropdownPosition, setDropdownPosition] = useState({ top: 100, right: 20 });

  // Event creation state
  const [showCreateEventModal, setShowCreateEventModal] = useState(false);
  const [newEventStartDate, setNewEventStartDate] = useState(null);
  const [newEventEndDate, setNewEventEndDate] = useState(null);

  // For drawer animation
  const translateX = useRef(new Animated.Value(-DRAWER_WIDTH)).current;

  // Check for openCreateModal parameter on mount
  useEffect(() => {
    if (params.openCreateModal === 'true') {
      const now = new Date();
      setNewEventStartDate(now);
      const endDate = new Date(now.getTime() + 60 * 60 * 1000);
      setNewEventEndDate(endDate);
      setShowCreateEventModal(true);
    }
  }, [params.openCreateModal]);

  // Set initial drawer state based on device type
  useEffect(() => {
    if (IS_TABLET) {
      // Open drawer by default on tablets
      setIsDrawerOpen(true);
      translateX.setValue(0);
    }
  }, []);

  // Available calendar view modes
  const viewModes = [
    { id: 'week', label: 'Week' },
    { id: 'day', label: 'Day' },
    { id: 'month', label: 'Month' },
    { id: 'year', label: 'Year' },
    { id: 'agenda', label: 'Agenda' },
  ];

  // Events state - will be populated from API
  const [events, setEvents] = useState([]);
  const [loading, setLoading] = useState(false);
  const [initialLoading, setInitialLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);
  const [savingEvent, setSavingEvent] = useState(false);

  // Add state for case selection
  const [showCaseSelectionModal, setShowCaseSelectionModal] = useState(false);
  const [selectedCaseId, setSelectedCaseId] = useState(null);

  // Add state for event filtering
  const [eventFilter, setEventFilter] = useState(caseId ? 'case' : 'all'); // 'all' or 'case'

  // Fetch events when component loads or filter changes
  useEffect(() => {
    const fetchEvents = async () => {
      if (!token) {
        setInitialLoading(false);
        return;
      }

      try {
        // Use initialLoading for first load, loading for subsequent loads
        if (events.length === 0) {
          setInitialLoading(true);
        } else {
          setLoading(true);
        }

        console.log('=== FETCHING EVENTS ===');
        console.log('Token available:', !!token);
        console.log('Event filter:', eventFilter);
        console.log('Case ID:', caseId);

        let response;

        // Determine which API to call based on filter and caseId
        if (eventFilter === 'case' && caseId) {
          // Fetch events for specific case
          console.log('Fetching case-specific events for case:', caseId);
          response = await apiService.fetchCaseEvents(token, caseId);
        } else {
          // Fetch all prosecutor events
          console.log('Fetching all prosecutor events');
          response = await apiService.fetchProsecutorEvents(token);
        }

        console.log('Events API Response:', JSON.stringify(response, null, 2));

        if (response.status === 'success' || response.data) {
          const eventsData = response.data || [];
          console.log('✅ Events fetched successfully:', eventsData.length, 'events');

          // Transform API data to calendar format
          const transformedEvents = eventsData.map(event => ({
            title: event.title,
            start: new Date(event.startDateTime),
            end: new Date(event.endDateTime),
            color: event.eventColor || '#0B36A1',
            details: event.description,
            id: event._id,
            caseId: event.caseId,
            duration: event.duration,
            attachmentUrl: event.attachmentUrl,
            assignedJudge: event.assignedJudge,
            assignedProsecutor: event.assignedProsecutor,
            assignedCourt: event.assignedCourt
          }));

          setEvents(transformedEvents);
          console.log('Events set in calendar:', transformedEvents.length);
        } else {
          console.log('No events found or API error:', response.message);
          setEvents([]);
        }
      } catch (error) {
        console.error('Error fetching events:', error.message);
        setEvents([]);
        Alert.alert('Error', 'Failed to load events. Please try again.');
      } finally {
        setInitialLoading(false);
        setLoading(false);
      }
    };

    fetchEvents();
  }, [token, eventFilter, caseId]);

  // Function to refresh events (can be called after creating new events)
  const refreshEvents = useCallback(async () => {
    if (!token) return;

    try {
      setRefreshing(true);
      console.log('🔄 Refreshing events...');
      console.log('Current filter:', eventFilter);
      console.log('Case ID:', caseId);

      let response;

      // Use same logic as initial fetch
      if (eventFilter === 'case' && caseId) {
        console.log('Refreshing case-specific events for case:', caseId);
        response = await apiService.fetchCaseEvents(token, caseId);
      } else {
        console.log('Refreshing all prosecutor events');
        response = await apiService.fetchProsecutorEvents(token);
      }

      if (response.status === 'success' || response.data) {
        const eventsData = response.data || [];
        const transformedEvents = eventsData.map(event => ({
          title: event.title,
          start: new Date(event.startDateTime),
          end: new Date(event.endDateTime),
          color: event.eventColor || '#0B36A1',
          details: event.description,
          id: event._id,
          caseId: event.caseId,
          duration: event.duration,
          attachmentUrl: event.attachmentUrl,
          assignedProsecutor: event.assignedProsecutor,
          assignedCourt: event.assignedCourt,
          assignedDispatcher: event.assignedDispatcher
        }));

        setEvents(transformedEvents);
        console.log('✅ Events refreshed:', transformedEvents.length, 'events');
      }
    } catch (error) {
      console.error('Error refreshing events:', error.message);
      Alert.alert('Error', 'Failed to refresh events. Please try again.');
    } finally {
      setRefreshing(false);
    }
  }, [token, eventFilter, caseId]);

  // Format date range for header
  const formatDateRange = useCallback(() => {
    const isCompact = !IS_TABLET; // Use compact format on mobile

    if (viewMode === 'day') {
      return selectedDate.toLocaleDateString('default', {
        day: 'numeric',
        month: isCompact ? 'short' : 'long',
        year: isCompact ? '2-digit' : 'numeric',
        weekday: isCompact ? 'short' : 'long'
      });
    } else if (viewMode === 'month') {
      return selectedDate.toLocaleDateString('default', {
        month: 'long',
        year: isCompact ? '2-digit' : 'numeric'
      });
    } else if (viewMode === 'year') {
      return selectedDate.getFullYear().toString();
    } else if (viewMode === 'agenda') {
      return 'Upcoming Events';
    } else {
      // Week view (default)
      const startOfWeek = new Date(selectedDate);
      startOfWeek.setDate(selectedDate.getDate() - selectedDate.getDay() + 1);

      const endOfWeek = new Date(startOfWeek);
      endOfWeek.setDate(startOfWeek.getDate() + 6);

      if (isCompact) {
        // Compact format for mobile: "1-7 Jan '23"
        return `${startOfWeek.getDate()}-${endOfWeek.getDate()} ${startOfWeek.toLocaleString('default', { month: 'short' })} '${startOfWeek.getFullYear().toString().slice(-2)}`;
      } else {
        // Full format for tablets: "1-7 January 2023"
        return `${startOfWeek.getDate()}-${endOfWeek.getDate()} ${startOfWeek.toLocaleString('default', { month: 'long' })} ${startOfWeek.getFullYear()}`;
      }
    }
  }, [selectedDate, viewMode]);

  // Format time for event display
  const formatEventTime = useCallback((date) => {
    if (!date) return '';

    // Handle both Date objects and timestamp numbers
    const dateObj = date instanceof Date ? date : new Date(date);

    // Check if the date is valid
    if (isNaN(dateObj.getTime())) return '';

    return dateObj.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' });
  }, []);

  // Toggle drawer open/close
  const toggleDrawer = useCallback(() => {
    if (isDrawerOpen) {
      // Close drawer
      Animated.timing(translateX, {
        toValue: -DRAWER_WIDTH,
        duration: 250,
        useNativeDriver: true,
      }).start(() => {
        setIsDrawerOpen(false);
      });
    } else {
      // Open drawer
      setIsDrawerOpen(true);

      // Animate drawer
      Animated.timing(translateX, {
        toValue: 0,
        duration: 250,
        useNativeDriver: true,
      }).start();
    }
  }, [isDrawerOpen, translateX]);

  // We're using toggleDrawer for all drawer operations

  // Handle date selection from mini calendar
  const handleDateSelect = useCallback((date) => {
    if (date) {
      setSelectedDate(date);
      // Close drawer on mobile only
      if (!IS_TABLET) {
        toggleDrawer();
      }
    }
  }, [toggleDrawer, IS_TABLET]);

  // Handle today button press
  const handleTodayPress = useCallback(() => {
    setSelectedDate(new Date());
  }, []);

  // Simplified handleCellPress - check if we have caseId and filter context for direct event creation
  const handleCellPress = useCallback((date) => {
    // Set the start time to the clicked time
    setNewEventStartDate(date);

    // Set the end time to 1 hour after the start time
    const endDate = new Date(date.getTime() + 60 * 60 * 1000);
    setNewEventEndDate(endDate);

    // Create event directly only if:
    // 1. We have a caseId from params AND
    // 2. Filter is set to 'case' (meaning user is viewing case-specific events)
    if (caseId && eventFilter === 'case') {
      // We have a case ID and user is in case context, so create event directly
      setShowCreateEventModal(true);
    } else {
      // Either no case ID or user is viewing all events, so show case selection
      setShowCaseSelectionModal(true);
    }
  }, [caseId, eventFilter]);

  // Handle event press - navigate to videoStatement for existing events
  const handleEventPress = useCallback((event) => {
    // Navigate to videoStatement with event details
    router.push({
      pathname: '(screensDispatchers)/videoStatement',
      params: {
        eventDetails: JSON.stringify({
          title: event.title,
          details: event.details,
          start: event.start,
          end: event.end,
          color: event.color,
          id: event.id,
          caseId: event.caseId,
          duration: event.duration,
          attachmentUrl: event.attachmentUrl,
          isUpdate: true,
          eventId: event.id
        })
      }
    });
  }, [router]);

  // Handle case selection - set the selected case and open event creation modal
  const handleCaseSelect = useCallback((selectedCaseId) => {
    setSelectedCaseId(selectedCaseId);
    setShowCaseSelectionModal(false);
    setShowCreateEventModal(true);
  }, []);

  // Handle saving event - simplified logic for case ID determination
  const handleSaveEvent = useCallback(async (eventData) => {
    try {
      setSavingEvent(true);

      // Check if token is available
      if (!token) {
        throw new Error('Authentication token not available');
      }

      // Determine caseId in this priority:
      // 1. From params (if page has caseId)
      // 2. From selected case in modal (if user selected from case selection)
      // 3. From event data if editing existing event
      const eventCaseId = caseId || selectedCaseId || (eventData.isUpdate ? eventData.caseId : null);

      if (!eventCaseId) {
        throw new Error('Case ID not available');
      }

      console.log('=== EVENT SAVE DEBUG ===');
      console.log('CaseId from params:', caseId);
      console.log('Selected CaseId:', selectedCaseId);
      console.log('Event CaseId:', eventData.caseId);
      console.log('Final CaseId:', eventCaseId);
      console.log('Token available:', !!token);
      console.log('Is Update:', eventData.isUpdate);

      // Prepare event data for API
      const apiEventData = {
        title: eventData.title,
        description: eventData.details && eventData.details.trim() ? eventData.details.trim() : 'Court hearing scheduled',
        caseId: eventCaseId,
        startDateTime: eventData.start.toISOString(),
        endDateTime: eventData.end.toISOString(),
        duration: Math.round((eventData.end - eventData.start) / (1000 * 60)), // in minutes
        eventColor: eventData.color || '#0B36A1',
        attachmentUrl: eventData.attachmentUrl || ''
      };

      let response;
      if (eventData.isUpdate) {
        // Update existing event
        console.log('Updating event with ID:', eventData.id);
        response = await apiService.updateEvent(token, eventData.id, apiEventData);
      } else {
        // Create new event
        console.log('Creating new event');
        response = await apiService.createEvent(token, apiEventData);
      }

      console.log('=== API RESPONSE ===');
      console.log('Full Response:', JSON.stringify(response, null, 2));
      console.log('Status:', response.status);
      console.log('Message:', response.message);

      if (response.status === 'success') {
        console.log('✅ Event saved successfully!');
        console.log('Event Data:', response.data);

        // Close modal and clear state
        setShowCreateEventModal(false);
        setSelectedCaseId(null); // Clear selected case

        // Refresh events list after saving event
        console.log('Event save completed successfully - refreshing events list');
        await refreshEvents();
      } else {
        throw new Error(response.message || 'Failed to save event');
      }

    } catch (error) {
      console.error('Error saving event:', error.message);
      Alert.alert('Error', error.message || 'Failed to save event. Please try again.');
    } finally {
      setSavingEvent(false);
    }
  }, [caseId, selectedCaseId, token, refreshEvents]);

  // Handle event press from sidebar
  const handleSidebarEventPress = useCallback((event) => {
    handleEventPress(event);
  }, [handleEventPress]);

  // Handle view mode selection
  const handleViewModeSelect = useCallback((mode) => {
    setViewMode(mode);
    setShowViewSelector(false);

    // Update viewYear when switching to year view
    if (mode === 'year') {
      setViewYear(selectedDate.getFullYear());
    }

    // Close drawer if switching to table view on mobile
    if (!IS_TABLET && mode === 'agenda' && isDrawerOpen) {
      toggleDrawer();
    }
  }, [toggleDrawer, selectedDate, isDrawerOpen, IS_TABLET]);

  // Toggle view selector modal
  const toggleViewSelector = useCallback(() => {
    if (!showViewSelector && weekButtonRef.current) {
      // Measure the position of the button to place the dropdown correctly
      weekButtonRef.current.measure((x, y, width, height, pageX, pageY) => {
        setDropdownPosition({
          top: pageY + height + 5, // Position below the button with a small gap
          left: pageX, // Align with the left edge of the button
        });
      });
    }
    setShowViewSelector(prev => !prev);
  }, [showViewSelector]);

  // Custom drawer content
  const renderDrawerContent = () => {
    // Helper function to check if a date is today
    const isToday = (date) => {
      const today = new Date();
      return date.getDate() === today.getDate() &&
        date.getMonth() === today.getMonth() &&
        date.getFullYear() === today.getFullYear();
    };

    // Helper function to check if a date is tomorrow
    const isTomorrow = (date) => {
      const tomorrow = new Date();
      tomorrow.setDate(tomorrow.getDate() + 1);
      return date.getDate() === tomorrow.getDate() &&
        date.getMonth() === tomorrow.getMonth() &&
        date.getFullYear() === tomorrow.getFullYear();
    };

    // Group events by day
    const groupedEvents = {
      today: events.filter(event => isToday(new Date(event.start))),
      tomorrow: events.filter(event => isTomorrow(new Date(event.start))),
      upcoming: events.filter(event => {
        const eventDate = new Date(event.start);
        return !isToday(eventDate) && !isTomorrow(eventDate) && eventDate > new Date();
      })
    };

    // Sort events by time within each group
    Object.keys(groupedEvents).forEach(key => {
      groupedEvents[key].sort((a, b) => new Date(a.start) - new Date(b.start));
    });

    // Format time (e.g., "09:00")
    const formatTimeShort = (date) => {
      const hours = date.getHours().toString().padStart(2, '0');
      const minutes = date.getMinutes().toString().padStart(2, '0');
      return `${hours}:${minutes}`;
    };

    // Format date range for upcoming events
    const formatDateRange = (startDate, endDate) => {
      const start = new Date(startDate);
      const end = new Date(endDate);

      const startDay = start.getDate().toString().padStart(2, '0');
      const startMonth = (start.getMonth() + 1).toString().padStart(2, '0');

      const endDay = end.getDate().toString().padStart(2, '0');
      const endMonth = (end.getMonth() + 1).toString().padStart(2, '0');

      return `${startDay}-${startMonth} to ${endDay}-${endMonth}`;
    };

    return (
      <View style={styles.drawerContainer}>
        {/* Mini calendar in drawer */}
        <MiniCalendar
          onDateSelect={handleDateSelect}
          selectedDate={selectedDate}
          loading={initialLoading}
        />

        {/* Events list */}
        <ScrollView style={styles.eventsContainer} showsVerticalScrollIndicator={false}>
          {/* Loading state for drawer events */}
          {initialLoading && (
            <View style={styles.loadingContainer}>
              <ActivityIndicator size="small" color={Colors.primary} />
              <Text style={styles.loadingText}>Loading events...</Text>
            </View>
          )}

          {/* Events content - only show when not loading */}
          {!initialLoading && (
            <>
              {/* Today's events */}
              {groupedEvents.today.length > 0 && (
            <View style={styles.daySection}>
              <View style={styles.daySectionHeader}>
                <Ionicons name="calendar-outline" size={20} color={Colors.primary} style={styles.daySectionIcon} />
                <Text style={styles.daySectionTitle}>Today</Text>
              </View>

              {groupedEvents.today.map((event, index) => (
                <TouchableOpacity
                  key={`today-${index}`}
                  style={styles.eventListItem}
                  onPress={() => handleSidebarEventPress(event)}
                  activeOpacity={0.7}
                >
                  <View style={[styles.eventDot, { backgroundColor: event.color }]} />
                  <Text style={styles.eventListTitle} numberOfLines={1}>{event.title}</Text>
                  <Text style={styles.eventListTime}>{formatTimeShort(new Date(event.start))}</Text>
                </TouchableOpacity>
              ))}
            </View>
          )}

          {/* Tomorrow's events */}
          {groupedEvents.tomorrow.length > 0 && (
            <View style={styles.daySection}>
              <View style={styles.daySectionHeader}>
                <Ionicons name="calendar-outline" size={20} color={Colors.primary} style={styles.daySectionIcon} />
                <Text style={styles.daySectionTitle}>Tomorrow</Text>
              </View>

              {groupedEvents.tomorrow.map((event, index) => (
                <TouchableOpacity
                  key={`tomorrow-${index}`}
                  style={styles.eventListItem}
                  onPress={() => handleSidebarEventPress(event)}
                  activeOpacity={0.7}
                >
                  <View style={[styles.eventDot, { backgroundColor: event.color }]} />
                  <Text style={styles.eventListTitle} numberOfLines={1}>{event.title}</Text>
                  <Text style={styles.eventListTime}>{formatTimeShort(new Date(event.start))}</Text>
                </TouchableOpacity>
              ))}
            </View>
          )}

          {/* Upcoming events (beyond tomorrow) */}
          {groupedEvents.upcoming.length > 0 && (
            <View style={styles.daySection}>
              <View style={styles.daySectionHeader}>
                <Ionicons name="calendar-outline" size={20} color={Colors.primary} style={styles.daySectionIcon} />
                <Text style={styles.daySectionTitle}>Upcoming</Text>
              </View>

              {groupedEvents.upcoming.map((event, index) => (
                <TouchableOpacity
                  key={`upcoming-${index}`}
                  style={styles.eventListItem}
                  onPress={() => handleSidebarEventPress(event)}
                  activeOpacity={0.7}
                >
                  <View style={[styles.eventDot, { backgroundColor: event.color }]} />
                  <Text style={styles.eventListTitle} numberOfLines={1}>{event.title}</Text>
                  <Text style={styles.eventListTime}>{formatTimeShort(new Date(event.start))}</Text>
                </TouchableOpacity>
              ))}
            </View>
          )}

          {/* Date range at the bottom */}
          {events.length > 0 && (
            <Text style={styles.dateRangeText}>
              {formatDateRange(
                events.reduce((min, e) => new Date(e.start) < min ? new Date(e.start) : min, new Date()),
                events.reduce((max, e) => new Date(e.end) > max ? new Date(e.end) : max, new Date())
              )}
            </Text>
          )}

              {/* No events message */}
              {events.length === 0 && !initialLoading && (
                <View style={styles.noEventsContainer}>
                  <Ionicons name="calendar-outline" size={48} color="#ccc" />
                  <Text style={styles.noEventsText}>No upcoming events</Text>
                </View>
              )}
            </>
          )}
        </ScrollView>
      </View>
    );
  };

  // Reusable MonthCalendar component
  const MonthCalendar = useCallback(({ month, year, isSelected, onPress }) => {
    // Function to check if a date has events
    const dateHasEvents = (year, month, day) => {
      return events.some(event => {
        const eventDate = event.start instanceof Date ? event.start : new Date(event.start);
        return (
          eventDate.getFullYear() === year &&
          eventDate.getMonth() === month &&
          eventDate.getDate() === day
        );
      });
    };

    // Function to generate days for a month calendar
    const generateMonthDays = () => {
      const firstDay = new Date(year, month, 1);
      const lastDay = new Date(year, month + 1, 0);

      const days = [];
      const daysInMonth = lastDay.getDate();

      // Get the day of the week for the first day (0 = Sunday, 1 = Monday, etc.)
      // For our calendar, we want Monday as day 0, so we need to adjust
      let firstDayOfWeek = firstDay.getDay(); // 0 (Sunday) to 6 (Saturday)

      // Convert to Monday-based index (0 = Monday, 6 = Sunday)
      firstDayOfWeek = firstDayOfWeek === 0 ? 6 : firstDayOfWeek - 1;

      // Add empty cells for days before the first day of the month
      for (let i = 0; i < firstDayOfWeek; i++) {
        days.push({ day: '', empty: true });
      }

      // Add days of the month
      for (let i = 1; i <= daysInMonth; i++) {
        const date = new Date(year, month, i);
        const isToday =
          date.getDate() === new Date().getDate() &&
          date.getMonth() === new Date().getMonth() &&
          date.getFullYear() === new Date().getFullYear();

        const hasEvent = dateHasEvents(year, month, i);

        days.push({
          day: i,
          date,
          isToday,
          hasEvent
        });
      }

      // Add empty cells at the end to complete the grid if needed
      // This ensures we have complete weeks
      const totalCells = Math.ceil(days.length / 7) * 7;
      const emptyCellsToAdd = totalCells - days.length;

      for (let i = 0; i < emptyCellsToAdd; i++) {
        days.push({ day: '', empty: true });
      }

      return days;
    };

    const monthName = new Date(year, month, 1).toLocaleString('default', { month: 'long' });
    const days = generateMonthDays();

    // Calculate optimal day cell size based on screen width and number of columns
    const getDayCellSize = () => {
      // Get the number of columns that will be used in the year view
      const getColumns = () => {
        if (SCREEN_WIDTH >= 1200) return 4; // Large screens - 4 columns
        if (SCREEN_WIDTH >= 768) return 3;  // Medium screens - 3 columns
        if (SCREEN_WIDTH >= 500) return 2;  // Small screens - 2 columns
        return 1;                           // Very small screens - 1 column
      };

      const columns = getColumns();

      // For single column layout (mobile), calculate width based on screen width
      if (columns === 1) {
        // Calculate available width for the calendar (screen width minus padding)
        const availableWidth = SCREEN_WIDTH - 40; // 20px padding on each side
        const cellWidth = Math.floor((availableWidth - 14) / 7); // 7 days, minus some margin
        return {
          width: cellWidth,
          height: cellWidth, // Make it square
          fontSize: Math.min(cellWidth - 4, 14) // Ensure text fits
        };
      }

      // For larger screens, use fixed sizes
      if (SCREEN_WIDTH >= 1200) return { width: 24, height: 24, fontSize: 14 };
      if (SCREEN_WIDTH >= 768) return { width: 22, height: 22, fontSize: 13 };
      if (SCREEN_WIDTH >= 500) return { width: 20, height: 20, fontSize: 12 };
      return { width: 18, height: 18, fontSize: 11 };
    };

    const { width, height, fontSize } = getDayCellSize();

    return (
      <TouchableOpacity
        style={[
          styles.monthCard,
          isSelected && styles.selectedMonthCard
        ]}
        onPress={onPress}
        activeOpacity={0.7}
      >
        <Text style={[
          styles.monthName,
          isSelected && styles.selectedMonthName
        ]}>
          {monthName}
        </Text>

        <View style={styles.miniCalendarContainer}>
          <View style={styles.miniDaysHeader}>
            {['M', 'T', 'W', 'T', 'F', 'S', 'S'].map((day, index) => (
              <Text key={index} style={[
                styles.miniDayLetter,
                { width: width }
              ]}>
                {day}
              </Text>
            ))}
          </View>

          <View style={styles.miniCalendarGrid}>
            {days.map((item, index) => (
              <View
                key={index}
                style={[
                  styles.miniCalendarDay,
                  { width: width, height: height },
                  // Check if this date is the selected date
                  (selectedDate.getDate() === item.day &&
                   selectedDate.getMonth() === month &&
                   selectedDate.getFullYear() === year) && styles.selectedCell,
                  item.empty && styles.miniEmptyCell
                ]}
              >
                {!item.empty && (
                  <>
                    <Text style={[
                      styles.miniCalendarDayText,
                      { fontSize: fontSize },
                      item.isToday && styles.todayCellText,
                      // Apply selected text style if this is the selected date
                      (selectedDate.getDate() === item.day &&
                       selectedDate.getMonth() === month &&
                       selectedDate.getFullYear() === year) && styles.selectedCellText
                    ]}>
                      {item.day}
                    </Text>
                    {item.isToday && <View style={[
                      styles.todayDot,
                      { width: fontSize / 3, height: fontSize / 3 }
                    ]} />}
                  </>
                )}
              </View>
            ))}
          </View>
        </View>
      </TouchableOpacity>
    );
  }, [events, SCREEN_WIDTH]);

  // Render year view (custom view for "Year" mode)
  const renderYearView = () => {
    // Function to handle month selection
    const handleMonthSelect = (monthIndex) => {
      const newDate = new Date(viewYear, monthIndex, 1);
      setSelectedDate(newDate);
      setViewMode('month');
    };

    // Function to change year
    const changeYear = (increment) => {
      setViewYear(prevYear => prevYear + increment);
    };

    // Determine number of columns based on screen width
    const getNumColumns = () => {
      if (SCREEN_WIDTH >= 1200) return 4; // Large screens - 4 columns
      if (SCREEN_WIDTH >= 768) return 3;  // Medium screens - 3 columns
      if (SCREEN_WIDTH >= 500) return 2;  // Small screens - 2 columns
      return 1;                           // Very small screens - 1 column
    };

    const numColumns = getNumColumns();

    // Calculate optimal padding based on screen size
    const getPadding = () => {
      if (SCREEN_WIDTH >= 1200) return 12;
      if (SCREEN_WIDTH >= 768) return 10;
      if (SCREEN_WIDTH >= 500) return 8;
      return 6;
    };

    return (
      <View style={styles.yearContainer}>
        {/* Year selector */}
        <View style={styles.yearSelectorContainer}>
          <TouchableOpacity
            style={styles.yearNavigationButton}
            onPress={() => changeYear(-1)}
          >
            <Ionicons name="chevron-back" size={24} color={Colors.primary} />
          </TouchableOpacity>

          <Text style={styles.yearSelectorText}>{viewYear}</Text>

          <TouchableOpacity
            style={styles.yearNavigationButton}
            onPress={() => changeYear(1)}
          >
            <Ionicons name="chevron-forward" size={24} color={Colors.primary} />
          </TouchableOpacity>
        </View>

        {/* Month grids */}
        <ScrollView showsVerticalScrollIndicator={false} contentContainerStyle={{ paddingBottom: 20 }}>
          <View style={[styles.yearGrid, { width: '100%' }]}>
            {Array.from({ length: 12 }, (_, monthIndex) => (
              <View
                key={monthIndex}
                style={{
                  width: `${100 / numColumns}%`,
                  padding: getPadding()
                }}
              >
                <MonthCalendar
                  month={monthIndex}
                  year={viewYear}
                  isSelected={
                    selectedDate.getMonth() === monthIndex &&
                    selectedDate.getFullYear() === viewYear
                  }
                  onPress={() => handleMonthSelect(monthIndex)}
                />
              </View>
            ))}
          </View>
        </ScrollView>
      </View>
    );
  };

  // Render agenda view (custom view for "Agenda" mode)
  const renderAgendaView = () => (
    <AgendaView
      events={events}
      formatEventTime={formatEventTime}
      onEventPress={handleEventPress}
      loading={initialLoading}
    />
  );

  // Handle filter toggle
  const handleFilterToggle = useCallback(() => {
    const newFilter = eventFilter === 'all' ? 'case' : 'all';
    setEventFilter(newFilter);
    console.log('Filter changed to:', newFilter);
  }, [eventFilter]);

  // Simplified "Add event" button handler
  const handleAddEventPress = useCallback(() => {
    const now = new Date();
    setNewEventStartDate(now);
    const endDate = new Date(now.getTime() + 60 * 60 * 1000);
    setNewEventEndDate(endDate);

    // Create event directly only if:
    // 1. We have a caseId from params AND
    // 2. Filter is set to 'case' (meaning user is viewing case-specific events)
    if (caseId && eventFilter === 'case') {
      // We have a case ID and user is in case context, so create event directly
      setShowCreateEventModal(true);
    } else {
      // Either no case ID or user is viewing all events, so show case selection
      setShowCaseSelectionModal(true);
    }
  }, [caseId, eventFilter]);

  return (
    <View style={styles.pageContainer}>
      {/* Custom calendar header */}
      <View style={styles.calendarHeader}>
        <View style={styles.dateSelector}>
          <TouchableOpacity
            style={styles.menuButton}
            onPress={toggleDrawer}
          >
            <Ionicons
              name={isDrawerOpen ? "close" : "menu"}
              size={24}
              color="#333"
            />
          </TouchableOpacity>
          <Text style={styles.dateRangeText}>{formatDateRange()}</Text>
          <TouchableOpacity
            ref={weekButtonRef}
            style={styles.weekButton}
            onPress={toggleViewSelector}
          >
            <Text style={styles.weekButtonText}>
              {viewModes.find(mode => mode.id === viewMode)?.label || 'Week'}
            </Text>
            <Ionicons name="chevron-down" size={16} color={Colors.primary} />
          </TouchableOpacity>
        </View>

        <View style={styles.controls}>
          <TouchableOpacity
            style={styles.todayButton}
            onPress={handleTodayPress}
          >
            <Text style={styles.todayButtonText}>Today</Text>
          </TouchableOpacity>

          {/* Refresh button */}
          <TouchableOpacity
            style={[styles.refreshButton, refreshing && styles.refreshButtonDisabled]}
            onPress={refreshEvents}
            disabled={refreshing}
          >
            <Ionicons
              name="refresh"
              size={16}
              color={refreshing ? "#999" : Colors.primary}
            />
          </TouchableOpacity>

          {/* Show filter button only when we have a caseId */}
          {caseId && (
            <TouchableOpacity
              style={[
                styles.filterButton,
                { backgroundColor: eventFilter === 'case' ? Colors.primary : '#f0f0f0' }
              ]}
              onPress={handleFilterToggle}
            >
              <Ionicons
                name={eventFilter === 'case' ? "funnel" : "funnel-outline"}
                size={16}
                color={eventFilter === 'case' ? "#fff" : "#666"}
              />
              <Text style={[
                styles.filterButtonText,
                { color: eventFilter === 'case' ? "#fff" : "#666" }
              ]}>
                {eventFilter === 'case' ? 'This Case' : 'All Events'}
              </Text>
            </TouchableOpacity>
          )}

          <TouchableOpacity
            style={styles.addButton}
            onPress={handleAddEventPress}
          >
            <Text style={styles.addButtonText}>Add event</Text>
            <Ionicons name="add" size={IS_TABLET ? 18 : 22} color="#fff" />
          </TouchableOpacity>
        </View>
      </View>

      {/* Main content area with drawer */}
      <View style={styles.contentContainer}>
        {/* Drawer - positioned absolutely for phones, side-by-side for tablets */}
        <Animated.View
          style={[
            styles.drawer,
            {
              transform: [{ translateX }],
              height: IS_TABLET ? SCREEN_HEIGHT - insets.top - 100 : '100%',
              top: 0,
              // For tablets, add border on the right side
              ...(IS_TABLET && {
                borderRightWidth: 1,
                borderRightColor: Colors.border,
              })
            }
          ]}
        >
          {renderDrawerContent()}
        </Animated.View>

        {/* Semi-transparent overlay when drawer is open (phones only) */}
        {isDrawerOpen && !IS_TABLET && (
          <TouchableOpacity
            style={styles.overlay}
            activeOpacity={1}
            onPress={toggleDrawer}
          />
        )}

        {/* Main calendar content - positioned correctly for both layouts */}
        <Animated.View
          style={[
            styles.mainContent,
            {
              // For tablets, use width and left positioning
              ...(IS_TABLET ? {
                width: isDrawerOpen ? SCREEN_WIDTH - DRAWER_WIDTH : SCREEN_WIDTH,
                left: isDrawerOpen ? DRAWER_WIDTH : 0
              } : {
                // For phones, keep content in place (drawer will overlay)
                width: SCREEN_WIDTH,
                left: 0,
                // No transform needed as drawer overlays content
                transform: [{ translateX: 0 }]
              })
            }
          ]}
        >
          {/* Loading overlay for initial load */}
          {initialLoading && (
            <View style={styles.loadingOverlay}>
              <ActivityIndicator size="large" color={Colors.primary} />
              <Text style={styles.loadingText}>Loading events...</Text>
            </View>
          )}

          {/* Loading overlay for filter changes */}
          {loading && !initialLoading && (
            <View style={styles.loadingOverlay}>
              <ActivityIndicator size="large" color={Colors.primary} />
              <Text style={styles.loadingText}>Updating events...</Text>
            </View>
          )}

          {/* Refreshing indicator */}
          {refreshing && (
            <View style={styles.refreshingIndicator}>
              <ActivityIndicator size="small" color={Colors.primary} />
              <Text style={styles.refreshingText}>Refreshing...</Text>
            </View>
          )}

          {!initialLoading && (
            <>
              {viewMode === 'agenda' ? (
                renderAgendaView()
              ) : viewMode === 'year' ? (
                renderYearView()
              ) : (
                <Calendar
                  events={events}
                  height={SCREEN_HEIGHT - 150}
                  mode={viewMode}
                  date={selectedDate}
                  onPressCell={date => {
                    // Update selected date and open event creation modal - event will be created via API
                    setSelectedDate(date);
                    handleCellPress(date);
                  }}
                  onPressEvent={handleEventPress}
                  // Don't automatically update selected date when calendar view changes
                  // This prevents the calendar from overriding our mini calendar selection
                  onChangeDate={() => {}}
                  weekStartsOn={1} // Start week on Monday
                  showTime={true}
                  swipeEnabled={true}
                  style={styles.calendar}
                  eventCellStyle={event => ({
                    backgroundColor: event.color || Colors.primary,
                    borderRadius: 4,
                  })}
                />
              )}
            </>
          )}
        </Animated.View>
      </View>

      {/* View mode selector modal */}
      <Modal
        visible={showViewSelector}
        transparent={true}
        animationType="fade"
        onRequestClose={() => setShowViewSelector(false)}
      >
        <TouchableOpacity
          style={styles.modalOverlay}
          activeOpacity={1}
          onPress={() => setShowViewSelector(false)}
        >
          <View
            style={[
              styles.viewSelectorContainer,
              dropdownPosition
            ]}
          >
            {viewModes.map(mode => (
              <TouchableOpacity
                key={mode.id}
                style={[
                  styles.viewOption,
                  viewMode === mode.id && styles.selectedViewOption
                ]}
                onPress={() => handleViewModeSelect(mode.id)}
              >
                <Text
                  style={[
                    styles.viewOptionText,
                    viewMode === mode.id && styles.selectedViewOptionText
                  ]}
                >
                  {mode.label}
                </Text>
                {viewMode === mode.id && (
                  <Ionicons name="checkmark" size={18} color={Colors.primary} />
                )}
              </TouchableOpacity>
            ))}
          </View>
        </TouchableOpacity>
      </Modal>

      {/* Create Event Modal */}
      <CreateEventModal
        visible={showCreateEventModal}
        onClose={() => {
          setShowCreateEventModal(false);
        }}
        onSave={handleSaveEvent}
        initialDate={newEventStartDate}
        initialEndDate={newEventEndDate}
        loading={savingEvent}
      />

      {/* Add CaseSelectionModal */}
      <CaseSelectionModal
        visible={showCaseSelectionModal}
        onClose={() => setShowCaseSelectionModal(false)}
        onCaseSelect={handleCaseSelect}
      />
    </View>
  );
};

export default ScheduleHearing;
