import React from 'react';
import { Safe<PERSON>reaProvider, SafeAreaView } from 'react-native-safe-area-context';
import { Stack } from 'expo-router';
import CustomStackHeader from '../../components/Larges/CustomStackHeader';

export default function MainLayoutProsecutor () {
  return (
    <SafeAreaProvider>
      <SafeAreaView style={{ flex: 1 }}>
        <Stack>
          <Stack.Screen
            name="(drawer)"
            options={{
              headerShown: false,
            }}
          />

          <Stack.Screen
            name="(screensDispatchers)/prosecutorRecentCases"
            options={{
              header: () => <CustomStackHeader title="Assigned Cases" 
                showBackButton={true}
                backButtonColor="#979797"
                showSearchIcon={false}
                onSearchPress={() => console.log('Search Recent Cases')}
                showNotificationIcon={false}
                showBorder={true} 
                borderColor="#D8D8D8"
              />, 
            }}
          />
          <Stack.Screen
            name="(screensDispatchers)/dipatchQr"
            options={{
              headerShown: false,
            }}
          />
       
     
        
          <Stack.Screen
            name="(screensDispatchers)/notificationsDispacher"
            options={{
              header: () => <CustomStackHeader title="Notifications"
                showBackButton={true}
                backButtonColor="#979797"
                showSearchIcon={false}
                onSearchPress={() => console.log('Search Recent Cases')}
                showNotificationIcon={false}
                showBorder={true} 
                borderColor="#D8D8D8"
              />, 
            }}
          />

<Stack.Screen
            name="(screensDispatchers)/caseDetails"
            options={{
              header: () => <CustomStackHeader title="Case Details"
                showBackButton={true}
                backButtonColor="#979797"
                showSearchIcon={false}
                onSearchPress={() => console.log('Search Recent Cases')}
                showNotificationIcon={false}
                showBorder={true} 
                borderColor="#D8D8D8"
              />, 
            }}
          />

            <Stack.Screen
            name="(screensDispatchers)/evidenceDetailsPosecutor"
            options={{
              header: () => <CustomStackHeader title="Evidence Details"
                showBackButton={true}
                backButtonColor="#979797"
                showSearchIcon={false}
                onSearchPress={() => console.log('Search Recent Cases')}
                showNotificationIcon={false}
                showBorder={true} 
                borderColor="#D8D8D8"
              />, 
            }}
          />

          <Stack.Screen
            name="(screensDispatchers)/scheduleHearingProsecutor"
            options={{
              header: () => <CustomStackHeader title="Schedule a Call"
                showBackButton={true}
                backButtonColor="#979797"
                showSearchIcon={false}
                onSearchPress={() => console.log('Search Recent Cases')}
                showNotificationIcon={false}
                showBorder={true} 
                borderColor="#D8D8D8"
              />, 
            }}
          />

          <Stack.Screen
            name="(screensDispatchers)/videoStatement"
            options={{
              header: () => <CustomStackHeader title="Event Details"
                showBackButton={true}
                backButtonColor="#979797"
                showSearchIcon={false}
                onSearchPress={() => console.log('Search Recent Cases')}
                showNotificationIcon={false}
                showBorder={true} 
                borderColor="#D8D8D8"
              />, 
            }}
          />
          
        </Stack>
      </SafeAreaView>
    </SafeAreaProvider>
  );
}