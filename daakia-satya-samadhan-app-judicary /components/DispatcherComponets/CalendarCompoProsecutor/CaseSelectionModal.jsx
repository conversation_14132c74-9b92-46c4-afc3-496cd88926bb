import React, { useState, useEffect, useCallback } from 'react';
import {
  View,
  Text,
  Modal,
  TouchableOpacity,
  FlatList,
  ActivityIndicator,
  TextInput,
  Dimensions,
  StyleSheet
} from 'react-native';
import FontAwesome from "@expo/vector-icons/FontAwesome";
import { useAuth } from '../../../context/auth-context';
import { apiService } from '../../../services/api';

const { width, height } = Dimensions.get('window');

// Utility functions
const formatDate = (dateString) => {
  try {
    const date = new Date(dateString);
    if (isNaN(date.getTime())) throw new Error('Invalid date');
    return date.toLocaleDateString('en-GB');
  } catch (e) {
    console.error('Date formatting error:', e);
    return 'Invalid Date';
  }
};

const formatTime = (timeString) => {
  try {
    const date = new Date(timeString);
    if (isNaN(date.getTime())) throw new Error('Invalid time');
    const hours = String(date.getUTCHours()).padStart(2, "0");
    const minutes = String(date.getUTCMinutes()).padStart(2, "0");
    return `${hours}:${minutes}`;
  } catch (e) {
    console.error('Time formatting error:', e);
    return 'Invalid Time';
  }
};

const CaseSelectionModal = ({ visible, onClose, onCaseSelect }) => {
  const [cases, setCases] = useState([]);
  const [searchQuery, setSearchQuery] = useState('');
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState(null);
  const { token } = useAuth();

  const fetchCases = useCallback(async () => {
    try {
      setIsLoading(true);
      const result = await apiService.fetchProsecutorCases(token, 1, 100);

      if (!result.data || !Array.isArray(result.data.cases)) {
        throw new Error('Invalid data format received');
      }

      const formattedCases = result.data.cases.map(item => ({
        id: item._id,
        CaseName: item.title || 'Untitled Case',
        time: formatTime(item.createdAt),
        date: formatDate(item.createdAt),
        timestamp: new Date(item.createdAt).getTime(),
      }));

      setCases(formattedCases);
      setError(null);
    } catch (err) {
      console.error('Failed to fetch cases:', err);
      setError('Failed to load cases. Please try again.');
    } finally {
      setIsLoading(false);
    }
  }, [token]);

  useEffect(() => {
    if (visible) {
      fetchCases();
    }
  }, [visible, fetchCases]);

  const filteredCases = cases.filter(item => 
    item.CaseName.toLowerCase().includes(searchQuery.toLowerCase())
  );

  const handleCaseSelect = (caseItem) => {
    onCaseSelect(caseItem.id);
    onClose();
  };

  const renderCaseItem = ({ item }) => (
    <TouchableOpacity
      style={styles.caseItem}
      onPress={() => handleCaseSelect(item)}
    >
      <View style={styles.caseInfo}>
        <FontAwesome name="folder" size={24} color="#0B36A1" />
        <Text style={styles.caseName}>{item.CaseName}</Text>
      </View>
      <View style={styles.caseDetails}>
        <Text style={styles.caseTime}>{item.time}</Text>
        <Text style={styles.caseDate}>{item.date}</Text>
      </View>
    </TouchableOpacity>
  );

  return (
    <Modal
      visible={visible}
      transparent={true}
      animationType="slide"
      onRequestClose={onClose}
    >
      <View style={styles.modalOverlay}>
        <View style={styles.modalContent}>
          <View style={styles.modalHeader}>
            <Text style={styles.modalTitle}>Select Case</Text>
            <TouchableOpacity onPress={onClose} style={styles.closeButton}>
              <FontAwesome name="times" size={24} color="#666" />
            </TouchableOpacity>
          </View>

          <View style={styles.searchContainer}>
            <FontAwesome name="search" size={20} color="#666" />
            <TextInput
              style={styles.searchInput}
              placeholder="Search cases..."
              value={searchQuery}
              onChangeText={setSearchQuery}
            />
            {searchQuery !== '' && (
              <TouchableOpacity onPress={() => setSearchQuery('')}>
                <FontAwesome name="times-circle" size={20} color="#666" />
              </TouchableOpacity>
            )}
          </View>

          {isLoading ? (
            <View style={styles.loadingContainer}>
              <ActivityIndicator size="large" color="#0B36A1" />
            </View>
          ) : error ? (
            <View style={styles.errorContainer}>
              <Text style={styles.errorText}>{error}</Text>
              <TouchableOpacity 
                style={styles.retryButton}
                onPress={fetchCases}
              >
                <Text style={styles.retryButtonText}>Retry</Text>
              </TouchableOpacity>
            </View>
          ) : (
            <FlatList
              data={filteredCases}
              renderItem={renderCaseItem}
              keyExtractor={item => item.id}
              ListEmptyComponent={
                <View style={styles.emptyContainer}>
                  <Text style={styles.emptyText}>
                    {searchQuery ? 'No matching cases found' : 'No cases found'}
                  </Text>
                </View>
              }
            />
          )}
        </View>
      </View>
    </Modal>
  );
};

const styles = StyleSheet.create({
  modalOverlay: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  modalContent: {
    width: width * 0.9,
    height: height * 0.8,
    backgroundColor: '#FFFFFF',
    borderRadius: 12,
    padding: 16,
  },
  modalHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 16,
  },
  modalTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#333',
    fontFamily: 'Roboto',
  },
  closeButton: {
    padding: 4,
  },
  searchContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#F5F5F5',
    borderRadius: 8,
    paddingHorizontal: 12,
    marginBottom: 16,
  },
  searchInput: {
    flex: 1,
    paddingVertical: 12,
    paddingHorizontal: 8,
    fontSize: 16,
    fontFamily: 'Roboto',
  },
  caseItem: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingVertical: 12,
    borderBottomWidth: 1,
    borderBottomColor: '#E5E5E5',
  },
  caseInfo: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 10,
    flex: 1,
  },
  caseName: {
    fontSize: 16,
    color: '#333',
    fontFamily: 'Roboto',
  },
  caseDetails: {
    alignItems: 'flex-end',
  },
  caseTime: {
    fontSize: 14,
    color: '#666',
    fontFamily: 'Roboto',
  },
  caseDate: {
    fontSize: 14,
    color: '#666',
    fontFamily: 'Roboto',
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  errorContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 20,
  },
  errorText: {
    color: 'red',
    textAlign: 'center',
    marginBottom: 20,
    fontFamily: 'Roboto',
  },
  retryButton: {
    backgroundColor: '#0B36A1',
    padding: 10,
    borderRadius: 5,
  },
  retryButtonText: {
    color: 'white',
    fontFamily: 'Roboto',
  },
  emptyContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 20,
  },
  emptyText: {
    color: '#666',
    fontFamily: 'Roboto',
  },
});

export default CaseSelectionModal;