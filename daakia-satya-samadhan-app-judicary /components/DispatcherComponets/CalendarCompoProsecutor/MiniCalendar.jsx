import React, { useState, useEffect, useMemo } from 'react';
import { View, Text, TouchableOpacity, StyleSheet, ActivityIndicator } from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { styles } from './styles/scheduleHearing.styles';
import { theme } from '../../../constants/colors';

const MiniCalendar = ({
  onDateSelect,
  selectedDate = new Date(),
  loading = false
}) => {
  // Maintain internal state for displayed month/year
  const [displayMonth, setDisplayMonth] = useState(selectedDate.getMonth());
  const [displayYear, setDisplayYear] = useState(selectedDate.getFullYear());

  // Update displayed month/year when selected date changes to a different month/year
  useEffect(() => {
    // Only update if the selected date is in a different month/year
    if (selectedDate.getMonth() !== displayMonth ||
        selectedDate.getFullYear() !== displayYear) {
      setDisplayMonth(selectedDate.getMonth());
      setDisplayYear(selectedDate.getFullYear());
    }
  }, [selectedDate]);

  // Navigate to previous month
  const goToPrevMonth = () => {
    if (displayMonth === 0) {
      setDisplayMonth(11);
      setDisplayYear(displayYear - 1);
    } else {
      setDisplayMonth(displayMonth - 1);
    }
  };

  // Navigate to next month
  const goToNextMonth = () => {
    if (displayMonth === 11) {
      setDisplayMonth(0);
      setDisplayYear(displayYear + 1);
    } else {
      setDisplayMonth(displayMonth + 1);
    }
  };

  // Get current month name
  const currentMonthName = new Date(displayYear, displayMonth, 1)
    .toLocaleString('default', { month: 'long', year: 'numeric' });

  // Generate days for mini calendar
  const calendarDays = useMemo(() => {
    const today = new Date();
    const firstDay = new Date(displayYear, displayMonth, 1);
    const lastDay = new Date(displayYear, displayMonth + 1, 0);

    const days = [];
    const daysInMonth = lastDay.getDate();

    // Get the day of the week for the first day (0 = Sunday, 1 = Monday, etc.)
    // For our calendar, we want Monday as day 0, so we need to adjust
    let firstDayOfWeek = firstDay.getDay(); // 0 (Sunday) to 6 (Saturday)

    // Convert to Monday-based index (0 = Monday, 6 = Sunday)
    firstDayOfWeek = firstDayOfWeek === 0 ? 6 : firstDayOfWeek - 1;

    // Add empty cells for days before the first day of the month
    for (let i = 0; i < firstDayOfWeek; i++) {
      days.push({ day: '', empty: true });
    }

    // Add days of the month
    for (let i = 1; i <= daysInMonth; i++) {
      const date = new Date(displayYear, displayMonth, i);

      days.push({
        day: i,
        date,
        isToday: i === today.getDate() &&
                 displayMonth === today.getMonth() &&
                 displayYear === today.getFullYear(),
        isSelected: selectedDate &&
                   i === selectedDate.getDate() &&
                   displayMonth === selectedDate.getMonth() &&
                   displayYear === selectedDate.getFullYear()
      });
    }

    // Add empty cells at the end to complete the grid if needed
    const totalCells = Math.ceil(days.length / 7) * 7;
    const emptyCellsToAdd = totalCells - days.length;

    for (let i = 0; i < emptyCellsToAdd; i++) {
      days.push({ day: '', empty: true });
    }

    return days;
  }, [displayMonth, displayYear, selectedDate]);

  return (
    <View style={styles.drawerCalendar}>
      {/* Month navigation header */}
      <View style={localStyles.monthNavContainer}>
        <TouchableOpacity onPress={goToPrevMonth} style={localStyles.navButton}>
          <Ionicons name="chevron-back" size={20} color={theme.colors.text.dark} />
        </TouchableOpacity>

        <Text style={localStyles.monthYearText}>{currentMonthName}</Text>

        <TouchableOpacity onPress={goToNextMonth} style={localStyles.navButton}>
          <Ionicons name="chevron-forward" size={20} color={theme.colors.text.dark} />
        </TouchableOpacity>
      </View>

      <View style={styles.daysHeader}>
        {['M', 'T', 'W', 'T', 'F', 'S', 'S'].map((day, index) => (
          <Text key={index} style={styles.dayLetter}>{day}</Text>
        ))}
      </View>

      <View style={styles.calendarGrid}>
        {loading ? (
          <View style={styles.miniCalendarLoading}>
            <ActivityIndicator size="small" color={theme.colors.primary} />
          </View>
        ) : (
          calendarDays.map((item, index) => (
            <TouchableOpacity
              key={index}
              style={[
                styles.calendarDay,
                item.isSelected && styles.selectedCell,
                item.empty && styles.emptyCell
              ]}
              disabled={item.empty}
              onPress={() => item.date && onDateSelect(item.date)}
            >
              <Text style={[
                styles.calendarDayText,
                item.isToday && styles.todayCellText,
                item.isSelected && styles.selectedCellText
              ]}>
                {item.day}
              </Text>
              {item.isToday && <View style={styles.todayDot} />}
            </TouchableOpacity>
          ))
        )}
      </View>
    </View>
  );
};

// Local styles for the month navigation
const localStyles = StyleSheet.create({
  monthNavContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 10,
    paddingHorizontal: 4,
  },
  monthYearText: {
    fontSize: 16,
    fontWeight: '500',
    color: theme.colors.text.dark,
    fontFamily: 'Roboto',
  },
  navButton: {
    padding: 4,
  }
});

export default MiniCalendar;