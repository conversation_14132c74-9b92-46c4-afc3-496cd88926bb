import React, { useState } from 'react';
import {
  View,
  Text,
  Modal,
  TouchableOpacity,
  StyleSheet,
  TouchableWithoutFeedback,
  ScrollView,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { IS_TABLET } from './styles/scheduleHearing.styles';
import { Colors } from '../../../constants/colors';

const TimePickerModal = ({
  visible,
  onClose,
  onTimeSelect,
  initialHour = 9,
  initialMinute = 0,
  is24Hour = false,
}) => {
  const [selectedHour, setSelectedHour] = useState(initialHour);
  const [selectedMinute, setSelectedMinute] = useState(initialMinute);
  const [selectedPeriod, setSelectedPeriod] = useState(initialHour >= 12 ? 'PM' : 'AM');

  // Generate hours based on 12/24 hour format
  const generateHours = () => {
    if (is24Hour) {
      return Array.from({ length: 24 }, (_, i) => i);
    } else {
      return Array.from({ length: 12 }, (_, i) => i === 0 ? 12 : i);
    }
  };

  // Generate minutes (0, 15, 30, 45)
  const generateMinutes = () => {
    return [0, 15, 30, 45];
  };

  const handleConfirm = () => {
    let hour = selectedHour;

    // Convert to 24-hour format if needed
    if (!is24Hour) {
      if (selectedPeriod === 'PM' && selectedHour !== 12) {
        hour = selectedHour + 12;
      } else if (selectedPeriod === 'AM' && selectedHour === 12) {
        hour = 0;
      }
    }

    onTimeSelect(hour, selectedMinute);
    onClose();
  };

  return (
    <Modal
      visible={visible}
      transparent
      animationType="fade"
      onRequestClose={onClose}
    >
      <TouchableWithoutFeedback onPress={onClose}>
        <View style={styles.modalOverlay}>
          <TouchableWithoutFeedback onPress={e => e.stopPropagation()}>
            <View style={styles.modalContent}>
              <View style={styles.modalHeader}>
                <Text style={styles.modalTitle}>Select Time</Text>
                <TouchableOpacity onPress={onClose} style={styles.closeButton}>
                  <Ionicons name="close" size={24} color="#666" />
                </TouchableOpacity>
              </View>

              <View style={styles.timePickerContainer}>
                {/* Hours */}
                <View style={styles.pickerColumn}>
                  <Text style={styles.pickerLabel}>Hour</Text>
                  <ScrollView
                    style={styles.pickerScrollView}
                    showsVerticalScrollIndicator={false}
                  >
                    {generateHours().map((hour) => (
                      <TouchableOpacity
                        key={`hour-${hour}`}
                        style={[
                          styles.timeOption,
                          selectedHour === hour && styles.selectedTimeOption
                        ]}
                        onPress={() => setSelectedHour(hour)}
                      >
                        <Text style={[
                          styles.timeText,
                          selectedHour === hour && styles.selectedTimeText
                        ]}>
                          {hour < 10 ? `0${hour}` : hour}
                        </Text>
                      </TouchableOpacity>
                    ))}
                  </ScrollView>
                </View>

                {/* Minutes */}
                <View style={styles.pickerColumn}>
                  <Text style={styles.pickerLabel}>Minute</Text>
                  <ScrollView
                    style={styles.pickerScrollView}
                    showsVerticalScrollIndicator={false}
                  >
                    {generateMinutes().map((minute) => (
                      <TouchableOpacity
                        key={`minute-${minute}`}
                        style={[
                          styles.timeOption,
                          selectedMinute === minute && styles.selectedTimeOption
                        ]}
                        onPress={() => setSelectedMinute(minute)}
                      >
                        <Text style={[
                          styles.timeText,
                          selectedMinute === minute && styles.selectedTimeText
                        ]}>
                          {minute < 10 ? `0${minute}` : minute}
                        </Text>
                      </TouchableOpacity>
                    ))}
                  </ScrollView>
                </View>

                {/* AM/PM (only for 12-hour format) */}
                {!is24Hour && (
                  <View style={styles.pickerColumn}>
                    <Text style={styles.pickerLabel}>AM/PM</Text>
                    <View style={styles.ampmContainer}>
                      <TouchableOpacity
                        style={[
                          styles.ampmOption,
                          selectedPeriod === 'AM' && styles.selectedAmpmOption
                        ]}
                        onPress={() => setSelectedPeriod('AM')}
                      >
                        <Text style={[
                          styles.ampmText,
                          selectedPeriod === 'AM' && styles.selectedAmpmText
                        ]}>
                          AM
                        </Text>
                      </TouchableOpacity>
                      <TouchableOpacity
                        style={[
                          styles.ampmOption,
                          selectedPeriod === 'PM' && styles.selectedAmpmOption
                        ]}
                        onPress={() => setSelectedPeriod('PM')}
                      >
                        <Text style={[
                          styles.ampmText,
                          selectedPeriod === 'PM' && styles.selectedAmpmText
                        ]}>
                          PM
                        </Text>
                      </TouchableOpacity>
                    </View>
                  </View>
                )}
              </View>

              <View style={styles.buttonContainer}>
                <TouchableOpacity
                  style={styles.cancelButton}
                  onPress={onClose}
                >
                  <Text style={styles.cancelButtonText}>Cancel</Text>
                </TouchableOpacity>
                <TouchableOpacity
                  style={styles.confirmButton}
                  onPress={handleConfirm}
                >
                  <Text style={styles.confirmButtonText}>Confirm</Text>
                </TouchableOpacity>
              </View>
            </View>
          </TouchableWithoutFeedback>
        </View>
      </TouchableWithoutFeedback>
    </Modal>
  );
};

const styles = StyleSheet.create({
  modalOverlay: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  modalContent: {
    width: IS_TABLET ? '60%' : '90%',
    maxWidth: 500,
    backgroundColor: Colors.background,
    borderRadius: 8,
    overflow: 'hidden',
  },
  modalHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: 16,
    backgroundColor: '#f5f5f5',
    borderBottomWidth: 1,
    borderBottomColor: Colors.border,
  },
  modalTitle: {
    fontSize: 18,
    fontWeight: '500',
    color: Colors.black,
  },
  closeButton: {
    padding: 4,
  },
  timePickerContainer: {
    flexDirection: 'row',
    padding: 16,
    justifyContent: 'space-around',
  },
  pickerColumn: {
    alignItems: 'center',
    width: '30%',
  },
  pickerLabel: {
    fontSize: 16,
    fontWeight: '500',
    marginBottom: 8,
    color: Colors.black,
  },
  pickerScrollView: {
    height: 200,
  },
  timeOption: {
    paddingVertical: 12,
    paddingHorizontal: 16,
    alignItems: 'center',
    justifyContent: 'center',
    borderRadius: 4,
  },
  selectedTimeOption: {
    backgroundColor: '#e6f2ff',
  },
  timeText: {
    fontSize: 18,
    color: Colors.black,
  },
  selectedTimeText: {
    color: Colors.primary,
    fontWeight: '500',
  },
  ampmContainer: {
    marginTop: 8,
  },
  ampmOption: {
    paddingVertical: 12,
    paddingHorizontal: 16,
    alignItems: 'center',
    justifyContent: 'center',
    borderRadius: 4,
    marginBottom: 8,
  },
  selectedAmpmOption: {
    backgroundColor: '#e6f2ff',
  },
  ampmText: {
    fontSize: 18,
    color: Colors.black,
  },
  selectedAmpmText: {
    color: Colors.primary,
    fontWeight: '500',
  },
  buttonContainer: {
    flexDirection: 'row',
    justifyContent: 'flex-end',
    padding: 16,
    borderTopWidth: 1,
    borderTopColor: Colors.border,
  },
  cancelButton: {
    paddingVertical: 8,
    paddingHorizontal: 16,
    marginRight: 8,
  },
  cancelButtonText: {
    color: Colors.lightText,
    fontSize: 16,
  },
  confirmButton: {
    backgroundColor: Colors.primary,
    paddingVertical: 8,
    paddingHorizontal: 16,
    borderRadius: 4,
  },
  confirmButtonText: {
    color: Colors.background,
    fontSize: 16,
    fontWeight: '500',
  },
});

export default TimePickerModal;
