import React, { useState } from 'react';
import {
  View,
  Text,
  Modal,
  TouchableOpacity,
  StyleSheet,
  TouchableWithoutFeedback,
  FlatList,
  TextInput,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { IS_TABLET } from './styles/scheduleHearing.styles';
import { Colors } from '../../../constants/colors';

// Common timezones
const TIMEZONES = [
  { id: 'UTC-12:00', label: '(UTC-12:00) International Date Line West' },
  { id: 'UTC-11:00', label: '(UTC-11:00) Coordinated Universal Time-11' },
  { id: 'UTC-10:00', label: '(UTC-10:00) Hawaii' },
  { id: 'UTC-09:00', label: '(UTC-09:00) Alaska' },
  { id: 'UTC-08:00', label: '(UTC-08:00) Pacific Time (US & Canada)' },
  { id: 'UTC-07:00', label: '(UTC-07:00) Mountain Time (US & Canada)' },
  { id: 'UTC-06:00', label: '(UTC-06:00) Central Time (US & Canada)' },
  { id: 'UTC-05:00', label: '(UTC-05:00) Eastern Time (US & Canada)' },
  { id: 'UTC-04:00', label: '(UTC-04:00) Atlantic Time (Canada)' },
  { id: 'UTC-03:30', label: '(UTC-03:30) Newfoundland' },
  { id: 'UTC-03:00', label: '(UTC-03:00) Brasilia' },
  { id: 'UTC-02:00', label: '(UTC-02:00) Mid-Atlantic' },
  { id: 'UTC-01:00', label: '(UTC-01:00) Azores' },
  { id: 'UTC+00:00', label: '(UTC+00:00) London, Dublin, Edinburgh' },
  { id: 'UTC+01:00', label: '(UTC+01:00) Berlin, Paris, Rome, Madrid' },
  { id: 'UTC+02:00', label: '(UTC+02:00) Athens, Istanbul, Cairo' },
  { id: 'UTC+03:00', label: '(UTC+03:00) Moscow, Baghdad, Kuwait' },
  { id: 'UTC+03:30', label: '(UTC+03:30) Tehran' },
  { id: 'UTC+04:00', label: '(UTC+04:00) Abu Dhabi, Dubai, Baku' },
  { id: 'UTC+04:30', label: '(UTC+04:30) Kabul' },
  { id: 'UTC+05:00', label: '(UTC+05:00) Islamabad, Karachi, Tashkent' },
  { id: 'UTC+05:30', label: '(UTC+05:30) Chennai, Kolkata, Mumbai, New Delhi' },
  { id: 'UTC+05:45', label: '(UTC+05:45) Kathmandu' },
  { id: 'UTC+06:00', label: '(UTC+06:00) Astana, Dhaka' },
  { id: 'UTC+06:30', label: '(UTC+06:30) Yangon (Rangoon)' },
  { id: 'UTC+07:00', label: '(UTC+07:00) Bangkok, Hanoi, Jakarta' },
  { id: 'UTC+08:00', label: '(UTC+08:00) Beijing, Hong Kong, Singapore' },
  { id: 'UTC+09:00', label: '(UTC+09:00) Tokyo, Seoul, Osaka' },
  { id: 'UTC+09:30', label: '(UTC+09:30) Adelaide, Darwin' },
  { id: 'UTC+10:00', label: '(UTC+10:00) Sydney, Melbourne, Brisbane' },
  { id: 'UTC+11:00', label: '(UTC+11:00) Vladivostok, Solomon Islands' },
  { id: 'UTC+12:00', label: '(UTC+12:00) Auckland, Wellington, Fiji' },
  { id: 'UTC+13:00', label: '(UTC+13:00) Samoa, Tonga, Kiribati' },
];

const TimezonePickerModal = ({
  visible,
  onClose,
  onTimezoneSelect,
  initialTimezone = 'UTC+05:30',
}) => {
  const [searchQuery, setSearchQuery] = useState('');
  const [selectedTimezone, setSelectedTimezone] = useState(
    TIMEZONES.find(tz => tz.id === initialTimezone) || TIMEZONES[0]
  );

  const filteredTimezones = TIMEZONES.filter(tz =>
    tz.label.toLowerCase().includes(searchQuery.toLowerCase())
  );

  const handleTimezoneSelect = (timezone) => {
    setSelectedTimezone(timezone);
    onTimezoneSelect(timezone);
    onClose();
  };

  return (
    <Modal
      visible={visible}
      transparent
      animationType="fade"
      onRequestClose={onClose}
    >
      <TouchableWithoutFeedback onPress={onClose}>
        <View style={styles.modalOverlay}>
          <TouchableWithoutFeedback onPress={e => e.stopPropagation()}>
            <View style={styles.modalContent}>
              <View style={styles.modalHeader}>
                <Text style={styles.modalTitle}>Select Timezone</Text>
                <TouchableOpacity onPress={onClose} style={styles.closeButton}>
                  <Ionicons name="close" size={24} color="#666" />
                </TouchableOpacity>
              </View>

              <View style={styles.searchContainer}>
                <Ionicons name="search" size={20} color="#666" style={styles.searchIcon} />
                <TextInput
                  style={styles.searchInput}
                  placeholder="Search timezones..."
                  value={searchQuery}
                  onChangeText={setSearchQuery}
                  placeholderTextColor="#999"
                />
              </View>

              <FlatList
                data={filteredTimezones}
                keyExtractor={(item) => item.id}
                renderItem={({ item }) => (
                  <TouchableOpacity
                    style={[
                      styles.timezoneOption,
                      selectedTimezone.id === item.id && styles.selectedTimezoneOption
                    ]}
                    onPress={() => handleTimezoneSelect(item)}
                  >
                    <Text style={[
                      styles.timezoneText,
                      selectedTimezone.id === item.id && styles.selectedTimezoneText
                    ]}>
                      {item.label}
                    </Text>
                    {selectedTimezone.id === item.id && (
                      <Ionicons name="checkmark" size={20} color="#1a73e8" />
                    )}
                  </TouchableOpacity>
                )}
                style={styles.timezoneList}
              />

              <View style={styles.buttonContainer}>
                <TouchableOpacity
                  style={styles.cancelButton}
                  onPress={onClose}
                >
                  <Text style={styles.cancelButtonText}>Cancel</Text>
                </TouchableOpacity>
              </View>
            </View>
          </TouchableWithoutFeedback>
        </View>
      </TouchableWithoutFeedback>
    </Modal>
  );
};

const styles = StyleSheet.create({
  modalOverlay: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  modalContent: {
    width: IS_TABLET ? '70%' : '90%',
    maxWidth: 600,
    height: IS_TABLET ? '70%' : '80%',
    backgroundColor: Colors.background,
    borderRadius: 8,
    overflow: 'hidden',
  },
  modalHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: 16,
    backgroundColor: '#f5f5f5',
    borderBottomWidth: 1,
    borderBottomColor: Colors.border,
  },
  modalTitle: {
    fontSize: 18,
    fontWeight: '500',
    color: Colors.black,
  },
  closeButton: {
    padding: 4,
  },
  searchContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 12,
    borderBottomWidth: 1,
    borderBottomColor: Colors.border,
  },
  searchIcon: {
    marginRight: 8,
  },
  searchInput: {
    flex: 1,
    fontSize: 16,
    color: Colors.black,
    padding: 4,
  },
  timezoneList: {
    flex: 1,
  },
  timezoneOption: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingVertical: 12,
    paddingHorizontal: 16,
    borderBottomWidth: 1,
    borderBottomColor: '#f0f0f0',
  },
  selectedTimezoneOption: {
    backgroundColor: '#f8f9fa',
  },
  timezoneText: {
    fontSize: 16,
    color: Colors.black,
    flex: 1,
  },
  selectedTimezoneText: {
    color: Colors.primary,
    fontWeight: '500',
  },
  buttonContainer: {
    flexDirection: 'row',
    justifyContent: 'flex-end',
    padding: 16,
    borderTopWidth: 1,
    borderTopColor: Colors.border,
  },
  cancelButton: {
    paddingVertical: 8,
    paddingHorizontal: 16,
  },
  cancelButtonText: {
    color: Colors.lightText,
    fontSize: 16,
  },
});

export default TimezonePickerModal;
