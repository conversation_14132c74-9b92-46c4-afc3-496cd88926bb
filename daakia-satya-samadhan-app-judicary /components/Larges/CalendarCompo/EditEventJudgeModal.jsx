import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  Modal,
  TouchableOpacity,
  TextInput,
  ScrollView,
  StyleSheet,
  Alert,
} from 'react-native';
import { Colors } from '../../../constants/colors';
import DatePickerModal from './DatePickerModal';
import TimePickerModal from './TimePickerModal';
import TimezonePickerModal from './TimezonePickerModal';

// Event color options
const EVENT_COLORS = [
  { id: 'primary', color: '#0B36A1', label: 'Blue (Primary)' },
  { id: 'purple', color: '#6200ee', label: 'Purple' },
  { id: 'blue', color: '#1a73e8', label: 'Light Blue' },
  { id: 'green', color: '#34A853', label: 'Green' },
  { id: 'red', color: '#EA4335', label: 'Red' },
  { id: 'orange', color: '#FBBC04', label: 'Orange' },
];

const EditEventJudgeModal = ({
  visible,
  onClose,
  onSave,
  eventData,
  loading = false,
}) => {
  const [formData, setFormData] = useState({
    title: '',
    description: '',
    eventDate: new Date(),
    eventEndDate: new Date(),
    eventColor: '#0B36A1',
    timezone: { id: 'UTC+05:30', label: '(UTC+05:30) Chennai, Kolkata, Mumbai, New Delhi' }
  });

  // Picker states
  const [showDatePicker, setShowDatePicker] = useState(false);
  const [showStartTimePicker, setShowStartTimePicker] = useState(false);
  const [showEndTimePicker, setShowEndTimePicker] = useState(false);
  const [showTimezonePicker, setShowTimezonePicker] = useState(false);

  // Format date for display (e.g., "01/04/2025")
  const formatDate = (date) => {
    if (!date) return '';
    const day = date.getDate().toString().padStart(2, '0');
    const month = (date.getMonth() + 1).toString().padStart(2, '0');
    const year = date.getFullYear();
    return `${day}/${month}/${year}`;
  };

  // Format time for display (e.g., "13:00 PM")
  const formatTime = (date) => {
    if (!date) return '';
    const hours = date.getHours();
    const minutes = date.getMinutes().toString().padStart(2, '0');
    const ampm = hours >= 12 ? 'PM' : 'AM';
    const displayHours = hours % 12 || 12;
    return `${displayHours}:${minutes} ${ampm}`;
  };

  // Initialize form data when modal opens
  useEffect(() => {
    if (visible && eventData) {
      // Convert ISO strings to Date objects
      const startDate = new Date(eventData.startDateTime || eventData.start);
      const endDate = new Date(eventData.endDateTime || eventData.end);
      
      setFormData({
        title: eventData.title || '',
        description: eventData.description || eventData.details || '',
        eventDate: startDate,
        eventEndDate: endDate,
        eventColor: eventData.eventColor || '#0B36A1',
        timezone: { id: 'UTC+05:30', label: '(UTC+05:30) Chennai, Kolkata, Mumbai, New Delhi' }
      });
    }
  }, [visible, eventData]);

  // Handle date selection
  const handleDateSelect = (date) => {
    // Keep the time from the current eventDate
    const newDate = new Date(date);
    newDate.setHours(
      formData.eventDate.getHours(),
      formData.eventDate.getMinutes(),
      formData.eventDate.getSeconds(),
      formData.eventDate.getMilliseconds()
    );
    
    // Update end date to maintain the same duration
    const duration = formData.eventEndDate.getTime() - formData.eventDate.getTime();
    const newEndDate = new Date(newDate.getTime() + duration);
    
    setFormData(prev => ({
      ...prev,
      eventDate: newDate,
      eventEndDate: newEndDate
    }));
  };

  // Handle start time selection
  const handleStartTimeSelect = (hour, minute) => {
    const newDate = new Date(formData.eventDate);
    newDate.setHours(hour, minute, 0, 0);
    
    // If end time is now before start time, adjust it
    let newEndDate = formData.eventEndDate;
    if (newEndDate <= newDate) {
      newEndDate = new Date(newDate);
      newEndDate.setHours(newDate.getHours() + 1);
    }
    
    setFormData(prev => ({
      ...prev,
      eventDate: newDate,
      eventEndDate: newEndDate
    }));
  };

  // Handle end time selection
  const handleEndTimeSelect = (hour, minute) => {
    const newEndDate = new Date(formData.eventEndDate);
    newEndDate.setHours(hour, minute, 0, 0);

    // Ensure end time is after start time
    if (newEndDate <= formData.eventDate) {
      newEndDate.setDate(newEndDate.getDate() + 1);
    }

    setFormData(prev => ({
      ...prev,
      eventEndDate: newEndDate
    }));
  };

  // Handle save
  const handleSave = async () => {
    if (!formData.description.trim()) {
      Alert.alert('Error', 'Event description is required.');
      return;
    }

    const updateData = {
      description: formData.description.trim(),
      startDateTime: formData.eventDate.toISOString(),
      endDateTime: formData.eventEndDate.toISOString(),
      eventColor: formData.eventColor,
      duration: Math.round((formData.eventEndDate - formData.eventDate) / (1000 * 60))
    };

    await onSave(updateData);
  };

  return (
    <Modal
      visible={visible}
      animationType="slide"
      transparent={true}
      onRequestClose={onClose}
    >
      <View style={styles.modalOverlay}>
        <View style={styles.modalContainer}>
          <View style={styles.modalHeader}>
            <Text style={styles.modalTitle}>Edit Event Details</Text>
            <TouchableOpacity
              style={styles.closeButton}
              onPress={onClose}
            >
              <Text style={styles.closeText}>✕</Text>
            </TouchableOpacity>
          </View>

          <ScrollView style={styles.modalContent} showsVerticalScrollIndicator={false}>
            {/* Title Display (Read-only) */}
            <View style={styles.inputGroup}>
              <Text style={styles.inputLabel}>Event Title (Read-only)</Text>
              <View style={[styles.textInput, styles.readOnlyInput]}>
                <Text style={styles.readOnlyText}>{formData.title}</Text>
              </View>
            </View>

            {/* Description Input */}
            <View style={styles.inputGroup}>
              <Text style={styles.inputLabel}>Description *</Text>
              <TextInput
                style={[styles.textInput, styles.textArea]}
                value={formData.description}
                onChangeText={(text) => setFormData(prev => ({ ...prev, description: text }))}
                placeholder="Enter event description"
                placeholderTextColor="#999"
                multiline={true}
                numberOfLines={4}
                textAlignVertical="top"
              />
            </View>

            {/* Date and Time Section */}
            <View style={styles.inputGroup}>
              <Text style={styles.inputLabel}>Date & Time</Text>
              <View style={styles.dateTimeContainer}>
                <TouchableOpacity
                  style={styles.dateContainer}
                  onPress={() => setShowDatePicker(true)}
                >
                  <Text style={styles.dateText}>{formatDate(formData.eventDate)}</Text>
                  <Text style={styles.dateIcon}>📅</Text>
                </TouchableOpacity>

                <View style={styles.timeRow}>
                  <TouchableOpacity
                    style={styles.timeContainer}
                    onPress={() => setShowStartTimePicker(true)}
                  >
                    <Text style={styles.timeText}>{formatTime(formData.eventDate)}</Text>
                    <Text style={styles.timeIcon}>⏰</Text>
                  </TouchableOpacity>

                  <Text style={styles.toText}>to</Text>

                  <TouchableOpacity
                    style={styles.timeContainer}
                    onPress={() => setShowEndTimePicker(true)}
                  >
                    <Text style={styles.timeText}>{formatTime(formData.eventEndDate)}</Text>
                    <Text style={styles.timeIcon}>⏰</Text>
                  </TouchableOpacity>
                </View>

                <TouchableOpacity
                  style={styles.timezoneContainer}
                  onPress={() => setShowTimezonePicker(true)}
                >
                  <Text style={styles.timezoneText}>{formData.timezone.id}</Text>
                  <Text style={styles.timezoneIcon}>🌍</Text>
                </TouchableOpacity>
              </View>
            </View>

            {/* Color Selection */}
            <View style={styles.inputGroup}>
              <Text style={styles.inputLabel}>Event Color</Text>
              <View style={styles.colorOptions}>
                {EVENT_COLORS.map((colorOption) => (
                  <TouchableOpacity
                    key={colorOption.id}
                    style={[
                      styles.colorOption,
                      { backgroundColor: colorOption.color },
                      formData.eventColor === colorOption.color && styles.selectedColorOption
                    ]}
                    onPress={() => setFormData(prev => ({ ...prev, eventColor: colorOption.color }))}
                  >
                    {formData.eventColor === colorOption.color && (
                      <Text style={styles.colorCheckmark}>✓</Text>
                    )}
                  </TouchableOpacity>
                ))}
              </View>
            </View>
          </ScrollView>

          <View style={styles.modalFooter}>
            <TouchableOpacity
              style={styles.cancelButton}
              onPress={onClose}
            >
              <Text style={styles.cancelButtonText}>Cancel</Text>
            </TouchableOpacity>
            
            <TouchableOpacity
              style={[styles.saveButton, loading && styles.saveButtonDisabled]}
              onPress={handleSave}
              disabled={loading}
            >
              <Text style={styles.saveButtonText}>
                {loading ? 'Saving...' : 'Save Changes'}
              </Text>
            </TouchableOpacity>
          </View>
        </View>
      </View>

      {/* Date/Time Picker Modals */}
      <DatePickerModal
        visible={showDatePicker}
        onClose={() => setShowDatePicker(false)}
        onDateSelect={handleDateSelect}
        selectedDate={formData.eventDate}
      />

      <TimePickerModal
        visible={showStartTimePicker}
        onClose={() => setShowStartTimePicker(false)}
        onTimeSelect={handleStartTimeSelect}
        initialHour={formData.eventDate.getHours()}
        initialMinute={formData.eventDate.getMinutes()}
      />

      <TimePickerModal
        visible={showEndTimePicker}
        onClose={() => setShowEndTimePicker(false)}
        onTimeSelect={handleEndTimeSelect}
        initialHour={formData.eventEndDate.getHours()}
        initialMinute={formData.eventEndDate.getMinutes()}
      />

      <TimezonePickerModal
        visible={showTimezonePicker}
        onClose={() => setShowTimezonePicker(false)}
        onTimezoneSelect={(timezone) => setFormData(prev => ({ ...prev, timezone }))}
        initialTimezone={formData.timezone.id}
      />
    </Modal>
  );
};

const styles = StyleSheet.create({
  modalOverlay: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    justifyContent: 'center',
    alignItems: 'center',
    padding: 20,
  },
  modalContainer: {
    backgroundColor: Colors.background,
    borderRadius: 12,
    width: '100%',
    maxHeight: '80%',
    elevation: 5,
    shadowColor: Colors.black,
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.25,
    shadowRadius: 3.84,
  },
  modalHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: 20,
    borderBottomWidth: 1,
    borderBottomColor: '#E5E5E5',
  },
  modalTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: Colors.black,
  },
  closeButton: {
    width: 30,
    height: 30,
    borderRadius: 15,
    backgroundColor: '#F5F5F5',
    justifyContent: 'center',
    alignItems: 'center',
  },
  closeText: {
    fontSize: 16,
    color: '#666',
    fontWeight: 'bold',
  },
  modalContent: {
    padding: 20,
    maxHeight: 400,
  },
  inputGroup: {
    marginBottom: 20,
  },
  inputLabel: {
    fontSize: 14,
    fontWeight: '600',
    color: Colors.black,
    marginBottom: 8,
  },
  textInput: {
    borderWidth: 1,
    borderColor: '#E5E5E5',
    borderRadius: 8,
    padding: 12,
    fontSize: 16,
    color: Colors.black,
    backgroundColor: Colors.background,
  },
  textArea: {
    height: 100,
    textAlignVertical: 'top',
  },
  readOnlyInput: {
    backgroundColor: '#F8F9FA',
    borderColor: '#E9ECEF',
  },
  readOnlyText: {
    color: '#6C757D',
    fontSize: 16,
  },
  dateTimeContainer: {
    gap: 12,
  },
  dateContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    borderWidth: 1,
    borderColor: '#E5E5E5',
    borderRadius: 8,
    padding: 12,
    backgroundColor: Colors.background,
  },
  dateText: {
    fontSize: 16,
    color: Colors.black,
  },
  dateIcon: {
    fontSize: 16,
  },
  timeRow: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 8,
  },
  timeContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    borderWidth: 1,
    borderColor: '#E5E5E5',
    borderRadius: 8,
    padding: 12,
    backgroundColor: Colors.background,
    flex: 1,
  },
  timeText: {
    fontSize: 16,
    color: Colors.black,
  },
  timeIcon: {
    fontSize: 16,
  },
  toText: {
    fontSize: 16,
    color: '#666',
    textAlign: 'center',
  },
  timezoneContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    borderWidth: 1,
    borderColor: '#E5E5E5',
    borderRadius: 8,
    padding: 12,
    backgroundColor: Colors.background,
  },
  timezoneText: {
    fontSize: 14,
    color: Colors.black,
  },
  timezoneIcon: {
    fontSize: 16,
  },
  colorOptions: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: 12,
  },
  colorOption: {
    width: 40,
    height: 40,
    borderRadius: 20,
    justifyContent: 'center',
    alignItems: 'center',
    borderWidth: 2,
    borderColor: 'transparent',
  },
  selectedColorOption: {
    borderColor: '#333',
    borderWidth: 3,
  },
  colorCheckmark: {
    color: 'white',
    fontSize: 18,
    fontWeight: 'bold',
  },
  modalFooter: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    padding: 20,
    borderTopWidth: 1,
    borderTopColor: '#E5E5E5',
  },
  cancelButton: {
    flex: 1,
    backgroundColor: '#F5F5F5',
    padding: 15,
    borderRadius: 8,
    marginRight: 10,
    alignItems: 'center',
  },
  cancelButtonText: {
    color: '#666',
    fontSize: 16,
    fontWeight: '600',
  },
  saveButton: {
    flex: 1,
    backgroundColor: Colors.primary,
    padding: 15,
    borderRadius: 8,
    marginLeft: 10,
    alignItems: 'center',
  },
  saveButtonDisabled: {
    backgroundColor: Colors.disabled,
    opacity: 0.7,
  },
  saveButtonText: {
    color: Colors.background,
    fontSize: 16,
    fontWeight: '600',
  },
});

export default EditEventJudgeModal;
