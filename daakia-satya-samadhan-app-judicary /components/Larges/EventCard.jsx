import React, { useState } from 'react';
import {
  View,
  Text,
  TouchableOpacity,
  StyleSheet,
  ActivityIndicator,
  Alert,
  Platform,
} from 'react-native';
import MaterialCommunityIcons from "@expo/vector-icons/MaterialCommunityIcons";
import { Colors } from '../../constants/colors';
import { transformUrl } from '../../utils/transformUrl';
import * as FileSystem from 'expo-file-system';
import * as IntentLauncher from 'expo-intent-launcher';
import * as Sharing from 'expo-sharing';
import * as WebBrowser from 'expo-web-browser';

const EventCard = ({ 
  event, 
  onAttachmentClick, 
  downloadingId, 
  showAttachments = true,
  onEventPress = null 
}) => {
  // Support both string and array for attachmentUrl
  let attachments = [];
  if (Array.isArray(event.attachmentUrl)) {
    attachments = event.attachmentUrl.filter(Boolean);
  } else if (typeof event.attachmentUrl === 'string' && event.attachmentUrl) {
    attachments = [event.attachmentUrl];
  }

  const maxChips = 4;
  const extraCount = attachments.length - maxChips;

  // Format date and time
  const formatDateTime = () => {
    if (!event.startDateTime || !event.endDateTime) return '';
    
    const startDate = new Date(event.startDateTime);
    const endDate = new Date(event.endDateTime);
    
    const dateStr = startDate.toLocaleString([], { 
      weekday: 'long', 
      year: 'numeric', 
      month: 'long', 
      day: 'numeric' 
    });
    
    const startTime = startDate.toLocaleTimeString([], { 
      hour: '2-digit', 
      minute: '2-digit' 
    });
    
    const endTime = endDate.toLocaleTimeString([], { 
      hour: '2-digit', 
      minute: '2-digit' 
    });
    
    return `${dateStr} ${startTime} - ${endTime}`;
  };

  const handleCardPress = () => {
    if (onEventPress) {
      onEventPress(event);
    }
  };

  const handleAttachmentPress = (url) => {
    if (onAttachmentClick) {
      onAttachmentClick(url, event.title);
    }
  };

  return (
    <TouchableOpacity 
      style={styles.card} 
      onPress={handleCardPress}
      activeOpacity={onEventPress ? 0.7 : 1}
    >
      <Text style={styles.cardTitle}>{event.title}</Text>
      
      {event.description && (
        <Text style={styles.cardDescription}>{event.description}</Text>
      )}
      
      <Text style={styles.cardDate}>{formatDateTime()}</Text>
      
      {showAttachments && attachments.length > 0 && (
        <>
          <Text style={styles.contentLabel}>Content</Text>
          <View style={styles.chipRow}>
            {attachments.slice(0, maxChips).map((url, i) => (
              <TouchableOpacity
                key={i}
                style={styles.chip}
                onPress={() => handleAttachmentPress(url)}
                disabled={downloadingId === `${event.title}-${url}`}
              >
                <MaterialCommunityIcons
                  name={url.toLowerCase().endsWith('.pdf') ? 'file-pdf-box' : 'file-document'}
                  size={18}
                  color={Colors.primary}
                  style={{ marginRight: 4 }}
                />
                <Text style={styles.chipText} numberOfLines={1}>
                  {url.split('/').pop().split('.')[0]}
                </Text>
                {downloadingId === `${event.title}-${url}` && (
                  <ActivityIndicator size="small" color={Colors.primary} style={{ marginLeft: 4 }} />
                )}
              </TouchableOpacity>
            ))}
            {extraCount > 0 && (
              <View style={styles.chip}>
                <Text style={[styles.chipText, { color: Colors.primary }]}>+{extraCount}</Text>
              </View>
            )}
          </View>
        </>
      )}
      
      {/* Show event color indicator */}
      {event.eventColor && (
        <View style={styles.colorIndicator}>
          <View 
            style={[
              styles.colorDot, 
              { backgroundColor: event.eventColor }
            ]} 
          />
        </View>
      )}
    </TouchableOpacity>
  );
};

const styles = StyleSheet.create({
  card: {
    backgroundColor: '#fff',
    borderRadius: 12,
    padding: 16,
    marginBottom: 18,
    marginHorizontal: 4,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.08,
    shadowRadius: 4,
    elevation: 2,
    position: 'relative',
  },
  cardTitle: {
    fontSize: 18,
    fontWeight: '500',
    color: Colors.black,
    marginBottom: 4,
    fontFamily: 'Roboto_medium',
  },
  cardDescription: {
    fontSize: 14,
    color: Colors.lightText,
    marginBottom: 8,
    lineHeight: 20,
  },
  cardDate: {
    fontSize: 14,
    color: Colors.lightText,
    marginBottom: 10,
  },
  contentLabel: {
    fontSize: 13,
    color: Colors.lightText,
    marginBottom: 8,
    fontWeight: 'bold',
  },
  chipRow: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    alignItems: 'center',
    marginBottom: 4,
  },
  chip: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#F5F5F5',
    borderRadius: 16,
    paddingHorizontal: 10,
    paddingVertical: 6,
    marginRight: 8,
    marginBottom: 6,
    minWidth: 40,
  },
  chipText: {
    fontSize: 13,
    color: Colors.black,
    maxWidth: 90,
  },
  colorIndicator: {
    position: 'absolute',
    top: 16,
    right: 16,
  },
  colorDot: {
    width: 12,
    height: 12,
    borderRadius: 6,
    borderWidth: 1,
    borderColor: '#E0E0E0',
  },
});

export default EventCard;
