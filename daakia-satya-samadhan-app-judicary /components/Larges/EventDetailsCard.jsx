import React from 'react';
import { View, Text, StyleSheet, TouchableOpacity } from 'react-native';
import { Ionicons } from '@expo/vector-icons';

const EventDetailsCard = ({ 
  event, 
  onEdit = null, 
  showEditButton = false,
  containerStyle = {} 
}) => {
  if (!event) {
    return (
      <View style={[styles.card, styles.errorCard, containerStyle]}>
        <Text style={styles.errorText}>No event data available</Text>
      </View>
    );
  }

  const formatDateTime = (dateString) => {
    if (!dateString) return 'Not specified';
    
    try {
      const date = new Date(dateString);
      return date.toLocaleString('en-US', {
        weekday: 'long',
        year: 'numeric',
        month: 'long',
        day: 'numeric',
        hour: '2-digit',
        minute: '2-digit',
        hour12: true
      });
    } catch (error) {
      return 'Invalid date';
    }
  };

  const formatDuration = (startDate, endDate) => {
    if (!startDate || !endDate) return 'Duration not available';
    
    try {
      const start = new Date(startDate);
      const end = new Date(endDate);
      const diffMs = end - start;
      const diffHours = Math.floor(diffMs / (1000 * 60 * 60));
      const diffMinutes = Math.floor((diffMs % (1000 * 60 * 60)) / (1000 * 60));
      
      if (diffHours > 0) {
        return `${diffHours}h ${diffMinutes}m`;
      } else {
        return `${diffMinutes}m`;
      }
    } catch (error) {
      return 'Duration calculation error';
    }
  };

  // Helper function to get the correct field value with fallbacks
  const getEventField = (primary, fallback) => {
    return event[primary] || event[fallback];
  };

  const eventColor = getEventField('color', 'eventColor');
  const eventDescription = getEventField('details', 'description');
  const startTime = getEventField('start', 'startDateTime');
  const endTime = getEventField('end', 'endDateTime');

  return (
    <View style={[styles.card, containerStyle]}>
      {/* Header with title and edit button */}
      <View style={styles.header}>
        <View style={styles.titleContainer}>
          <View
            style={[
              styles.colorIndicator,
              { backgroundColor: eventColor || '#007AFF' }
            ]}
          />
          <Text style={styles.title} numberOfLines={2}>
            {event.title || 'Untitled Event'}
          </Text>
        </View>
        {showEditButton && onEdit && (
          <TouchableOpacity
            style={styles.editButton}
            onPress={() => onEdit(event)}
          >
            <Ionicons name="pencil" size={20} color="#007AFF" />
          </TouchableOpacity>
        )}
      </View>

      {/* Event Details */}
      {eventDescription && (
        <View style={styles.detailsSection}>
          <Text style={styles.sectionLabel}>Description</Text>
          <Text style={styles.detailsText}>{eventDescription}</Text>
        </View>
      )}

      {/* Attachments Content */}
      {(() => {
        const getFileTypeAndName = (url) => {
          if (!url) return { type: 'unknown', name: 'Unknown' }
          const parts = url.split('/')
          const fileName = parts[parts.length - 1]
          const ext = fileName.split('.').pop().toLowerCase()
          let type = 'file'
          if (ext === 'pdf') type = 'pdf'
          else if (["mp4", "mov", "avi"].includes(ext)) type = 'video'
          else if (["jpg", "jpeg", "png", "gif", "bmp", "webp"].includes(ext)) type = 'image'
          return { type, name: fileName }
        }
        const attachmentUrls = event?.attachmentUrl
          ? event.attachmentUrl.split(',').map(s => s.trim()).filter(Boolean)
          : []
        const maxVisible = 4
        const visibleAttachments = attachmentUrls.slice(0, maxVisible)
        const extraCount = attachmentUrls.length - maxVisible
        if (attachmentUrls.length === 0) return null
        return (
          <View style={{ marginBottom: 16 }}>
            <Text style={styles.sectionLabel}>Content</Text>
            <View style={{ flexDirection: 'row', flexWrap: 'wrap', marginTop: 4 }}>
              {visibleAttachments.map((url, idx) => {
                const { type, name } = getFileTypeAndName(url)
                return (
                  <View
                    key={url}
                    style={{
                      backgroundColor: '#f0f0f0',
                      borderRadius: 16,
                      paddingHorizontal: 10,
                      paddingVertical: 4,
                      marginRight: 8,
                      marginBottom: 8,
                      flexDirection: 'row',
                      alignItems: 'center',
                    }}
                  >
                    <Text style={{ fontWeight: 'bold', marginRight: 4 }}>
                      {type === 'pdf' && '📄'}
                      {type === 'video' && '🎥'}
                      {type === 'image' && '🖼️'}
                      {type === 'file' && '📎'}
                    </Text>
                    <Text numberOfLines={1} style={{ maxWidth: 80 }}>{name}</Text>
                  </View>
                )
              })}
              {extraCount > 0 && (
                <View
                  style={{
                    backgroundColor: '#e0e0e0',
                    borderRadius: 16,
                    paddingHorizontal: 10,
                    paddingVertical: 4,
                    marginRight: 8,
                    marginBottom: 8,
                    justifyContent: 'center',
                    alignItems: 'center',
                  }}
                >
                  <Text style={{ fontWeight: 'bold' }}>+{extraCount}</Text>
                </View>
              )}
            </View>
          </View>
        )
      })()}

      {/* Time Information */}
      <View style={styles.timeSection}>
        <View style={styles.timeRow}>
          <Ionicons name="time-outline" size={18} color="#666" />
          <View style={styles.timeInfo}>
            <Text style={styles.timeLabel}>Start Time</Text>
            <Text style={styles.timeValue}>
              {formatDateTime(startTime)}
            </Text>
          </View>
        </View>

        <View style={styles.timeRow}>
          <Ionicons name="time-outline" size={18} color="#666" />
          <View style={styles.timeInfo}>
            <Text style={styles.timeLabel}>End Time</Text>
            <Text style={styles.timeValue}>
              {formatDateTime(endTime)}
            </Text>
          </View>
        </View>

        <View style={styles.timeRow}>
          <Ionicons name="hourglass-outline" size={18} color="#666" />
          <View style={styles.timeInfo}>
            <Text style={styles.timeLabel}>Duration</Text>
            <Text style={styles.timeValue}>
              {formatDuration(startTime, endTime)}
            </Text>
          </View>
        </View>
      </View>

      {/* Additional Information */}
      <View style={styles.additionalInfo}>
        {event.timezone && (
          <View style={styles.infoRow}>
            <Ionicons name="globe-outline" size={16} color="#888" />
            <Text style={styles.infoText}>Timezone: {event.timezone}</Text>
          </View>
        )}
        
        {event.eventId && (
          <View style={styles.infoRow}>
            <Ionicons name="id-card-outline" size={16} color="#888" />
            <Text style={styles.infoText}>Event ID: {event.eventId}</Text>
          </View>
        )}

        {event.isUpdate !== undefined && (
          <View style={styles.infoRow}>
            <Ionicons 
              name={event.isUpdate ? "checkmark-circle-outline" : "add-circle-outline"} 
              size={16} 
              color={event.isUpdate ? "#4CAF50" : "#FF9800"} 
            />
            <Text style={[
              styles.infoText, 
              { color: event.isUpdate ? "#4CAF50" : "#FF9800" }
            ]}>
              {event.isUpdate ? "Updated Event" : "New Event"}
            </Text>
          </View>
        )}
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  card: {
    backgroundColor: '#FFFFFF',
    borderRadius: 12,
    padding: 20,
    marginVertical: 8,
    marginHorizontal: 16,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.1,
    shadowRadius: 3.84,
    elevation: 5,
    borderWidth: 1,
    borderColor: '#E5E5E5',
  },
  errorCard: {
    backgroundColor: '#FFF5F5',
    borderColor: '#FEB2B2',
  },
  errorText: {
    color: '#E53E3E',
    fontSize: 16,
    textAlign: 'center',
    fontWeight: '500',
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-start',
    marginBottom: 16,
  },
  titleContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
    marginRight: 12,
  },
  colorIndicator: {
    width: 4,
    height: 24,
    borderRadius: 2,
    marginRight: 12,
  },
  title: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#1A1A1A',
    flex: 1,
  },
  editButton: {
    padding: 8,
    borderRadius: 8,
    backgroundColor: '#F0F8FF',
  },
  detailsSection: {
    marginBottom: 16,
  },
  sectionLabel: {
    fontSize: 14,
    fontWeight: '600',
    color: '#666',
    marginBottom: 6,
  },
  detailsText: {
    fontSize: 16,
    color: '#333',
    lineHeight: 22,
  },
  timeSection: {
    marginBottom: 16,
  },
  timeRow: {
    flexDirection: 'row',
    alignItems: 'flex-start',
    marginBottom: 12,
  },
  timeInfo: {
    marginLeft: 12,
    flex: 1,
  },
  timeLabel: {
    fontSize: 14,
    fontWeight: '600',
    color: '#666',
    marginBottom: 2,
  },
  timeValue: {
    fontSize: 15,
    color: '#333',
    lineHeight: 20,
  },
  additionalInfo: {
    borderTopWidth: 1,
    borderTopColor: '#E5E5E5',
    paddingTop: 16,
  },
  infoRow: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 8,
  },
  infoText: {
    fontSize: 14,
    color: '#666',
    marginLeft: 8,
  },
});

export default EventDetailsCard;
