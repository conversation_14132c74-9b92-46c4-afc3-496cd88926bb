import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ActivityIndicator,
  Alert,
  Modal,
  Platform,
} from 'react-native';
import { Colors } from '../../constants/colors';
import { apiService } from '../../services/api';
import { useAuth } from '../../context/auth-context';
import EventCard from './EventCard';
import PreviewComponent from './PreviewComponent';
import { transformUrl } from '../../utils/transformUrl';
import * as FileSystem from 'expo-file-system';
import * as IntentLauncher from 'expo-intent-launcher';
import * as Sharing from 'expo-sharing';
import * as WebBrowser from 'expo-web-browser';

const EventsList = ({ 
  caseId, 
  title = "Court Proceedings",
  onEventPress = null,
  showAttachments = true,
  refreshTrigger = 0 
}) => {
  const [events, setEvents] = useState([]);
  const [loading, setLoading] = useState(true);
  const [downloadingId, setDownloadingId] = useState(null);
  const [previewUri, setPreviewUri] = useState(null);
  const [showPreview, setShowPreview] = useState(false);
  const { token } = useAuth();

  useEffect(() => {
    if (token && caseId) {
      fetchEvents();
    }
  }, [caseId, token, refreshTrigger]);

  const fetchEvents = async () => {
    try {
      setLoading(true);
      const result = await apiService.fetchCaseEvents(token, caseId);
      console.log('Case Events Result:', JSON.stringify(result, null, 2));
      
      if (result.status === 'success') {
        setEvents(result.data || []);
      } else {
        console.error('Failed to fetch case events:', result.message);
        Alert.alert('Error', 'Failed to fetch case events');
      }
    } catch (error) {
      console.error('Error fetching events:', error);
      Alert.alert('Error', 'Failed to fetch case events');
    } finally {
      setLoading(false);
    }
  };

  const downloadAndOpenPdf = async (url, title) => {
    if (!url) {
      Alert.alert('Error', 'No file URL provided');
      return;
    }
    
    try {
      setDownloadingId(`${title}-${url}`);
      const transformedUrl = transformUrl(url);
      const filename = url.split('/').pop();
      const localUri = `${FileSystem.documentDirectory}${filename}`;
      
      const downloadResumable = FileSystem.createDownloadResumable(
        transformedUrl,
        localUri,
        {},
        (downloadProgress) => {
          const progress = downloadProgress.totalBytesWritten / downloadProgress.totalBytesExpectedToWrite;
          console.log(`Download progress: ${progress * 100}%`);
        }
      );
      
      const { uri } = await downloadResumable.downloadAsync();
      
      if (Platform.OS === 'ios') {
        const canShare = await Sharing.isAvailableAsync();
        if (canShare) {
          await Sharing.shareAsync(uri);
        } else {
          await WebBrowser.openBrowserAsync(transformedUrl);
        }
      } else if (Platform.OS === 'android') {
        const contentUri = await FileSystem.getContentUriAsync(uri);
        await IntentLauncher.startActivityAsync('android.intent.action.VIEW', {
          data: contentUri,
          flags: 1,
          type: 'application/pdf',
        });
      }
    } catch (error) {
      console.error('Error downloading or opening PDF:', error);
      Alert.alert('Error', 'Could not download or open the PDF file');
    } finally {
      setDownloadingId(null);
    }
  };

  const handleAttachmentClick = (url, title) => {
    if (!url) {
      Alert.alert('Error', 'No file URL provided');
      return;
    }
    
    const isImageFile = /\.(jpg|jpeg|png|gif|webp)$/i.test(url);
    if (isImageFile) {
      setPreviewUri(transformUrl(url));
      setShowPreview(true);
    } else {
      downloadAndOpenPdf(url, title);
    }
  };

  const closePreview = () => {
    setShowPreview(false);
    setPreviewUri(null);
  };

  const handleEventPress = (event) => {
    if (onEventPress) {
      onEventPress(event);
    }
  };

  if (loading) {
    return (
      <View style={styles.container}>
        <Text style={styles.title}>{title}</Text>
        <View style={styles.loadingContainer}>
          <ActivityIndicator size="large" color={Colors.primary} />
          <Text style={styles.loadingText}>Loading events...</Text>
        </View>
      </View>
    );
  }

  return (
    <View style={styles.container}>
      <Text style={styles.title}>{title}</Text>
      
      {events.length > 0 ? (
        events.map((event, idx) => (
          <EventCard
            key={event._id || idx}
            event={event}
            onAttachmentClick={showAttachments ? handleAttachmentClick : null}
            downloadingId={downloadingId}
            showAttachments={showAttachments}
            onEventPress={onEventPress ? handleEventPress : null}
          />
        ))
      ) : (
        <View style={styles.emptyContainer}>
          <Text style={styles.emptyText}>No events found for this case</Text>
        </View>
      )}

      {/* Preview Modal */}
      <Modal
        visible={showPreview}
        transparent={true}
        animationType="fade"
        onRequestClose={closePreview}
      >
        <PreviewComponent 
          uri={previewUri} 
          onClose={closePreview} 
        />
      </Modal>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    paddingHorizontal: 10,
 
  },
  title: {
    fontSize: 18,
    fontWeight: '500',
    marginBottom: 16,
    fontFamily: 'Roboto_bold',
    color: Colors.primary,
  },
  loadingContainer: {
    alignItems: 'center',
    paddingVertical: 40,
  },
  loadingText: {
    marginTop: 12,
    fontSize: 16,
    color: Colors.lightText,
  },
  emptyContainer: {
    alignItems: 'center',
    paddingVertical: 40,
  },
  emptyText: {
    fontSize: 16,
    color: Colors.lightText,
    textAlign: 'center',
  },
});

export default EventsList;
