import { View, Text, StyleSheet, TouchableOpacity } from 'react-native'
import React, { useCallback, useState, useRef } from 'react'
import { Camera, useCameraDevice, useCameraPermission } from 'react-native-vision-camera'
import { Video } from 'expo-av'

const VisionCameraVideo = ({ onVideoRecorded, onClose }) => {
  const { hasPermission, requestPermission } = useCameraPermission()
  const device = useCameraDevice('back')
  const camera = useRef(null)
  const [isRecording, setIsRecording] = useState(false)
  const [recordedVideo, setRecordedVideo] = useState(null)
  const [showPreview, setShowPreview] = useState(false)

  React.useEffect(() => {
    if (!hasPermission) {
      requestPermission()
    }
  }, [hasPermission, requestPermission])

  const startRecording = useCallback(async () => {
    try {
      if (camera.current) {
        setIsRecording(true)
        setShowPreview(false)
        await camera.current.startRecording({
          fileType: 'mp4',
          videoCodec: 'h264',
          onRecordingFinished: (video) => {
            console.log('=== VISION CAMERA VIDEO RECORDED ===')
            console.log('Video object:', video)
            console.log('Video path:', video.path)
            console.log('Video size:', video.size)
            console.log('Video duration:', video.duration)
            setRecordedVideo(video)
            setShowPreview(true)
          },
          onRecordingError: (error) => {
            console.error('Failed to record video:', error)
          },
        })
      }
    } catch (error) {
      console.error('Failed to start recording:', error)
    }
  }, [])

  const stopRecording = useCallback(async () => {
    try {
      if (camera.current) {
        await camera.current.stopRecording()
        setIsRecording(false)
      }
    } catch (error) {
      console.error('Failed to stop recording:', error)
    }
  }, [])

  const retakeVideo = () => {
    setShowPreview(false)
    setRecordedVideo(null)
  }

  const handleDone = () => {
    if (recordedVideo) {
      console.log('=== HANDLE DONE DEBUG ===')
      console.log('Recorded video object:', recordedVideo)
      console.log('Passing path to parent:', recordedVideo.path)
      onVideoRecorded(recordedVideo.path)
      onClose()
    } else {
      console.error('No recorded video available!')
    }
  }

  if (!hasPermission) {
    return (
      <View style={styles.container}>
        <Text style={styles.text}>No access to camera</Text>
        <TouchableOpacity 
          style={styles.permissionButton}
          onPress={requestPermission}
        >
          <Text style={styles.buttonText}>Grant Permission</Text>
        </TouchableOpacity>
      </View>
    )
  }

  if (!device) {
    return (
      <View style={styles.container}>
        <Text style={styles.text}>No camera device found</Text>
      </View>
    )
  }

  if (showPreview && recordedVideo) {
    return (
      <View style={styles.container}>
        <Video
          style={StyleSheet.absoluteFill}
          source={{ uri: recordedVideo.path }}
          useNativeControls
          resizeMode="contain"
          isLooping
          shouldPlay
        />
        <View style={styles.previewControls}>
          <View style={styles.buttonRow}>
            <TouchableOpacity
              style={[styles.actionButton, styles.retakeButton]}
              onPress={retakeVideo}
            >
              <Text style={styles.buttonText}>Retake</Text>
            </TouchableOpacity>
            <TouchableOpacity
              style={[styles.actionButton, styles.doneButton]}
              onPress={handleDone}
            >
              <Text style={styles.buttonText}>Done</Text>
            </TouchableOpacity>
          </View>
        </View>
      </View>
    )
  }

  return (
    <View style={styles.container}>
      <Camera
        ref={camera}
        style={StyleSheet.absoluteFill}
        device={device}
        isActive={true}
        video={true}
      />
      <View style={styles.controlsContainer}>
        <TouchableOpacity
          style={[styles.recordButton, isRecording && styles.recordingButton]}
          onPress={isRecording ? stopRecording : startRecording}
        >
          <View style={[styles.recordButtonInner, isRecording && styles.recordingButtonInner]} />
        </TouchableOpacity>
      </View>
    </View>
  )
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: 'black',
  },
  text: {
    color: 'white',
    fontSize: 16,
    textAlign: 'center',
    marginTop: 20,
  },
  controlsContainer: {
    position: 'absolute',
    bottom: 40,
    left: 0,
    right: 0,
    alignItems: 'center',
    zIndex: 1000,
  },
  recordButton: {
    width: 80,
    height: 80,
    borderRadius: 40,
    backgroundColor: 'rgba(255, 255, 255, 0.3)',
    justifyContent: 'center',
    alignItems: 'center',
    borderWidth: 4,
    borderColor: 'white',
  },
  recordingButton: {
    borderColor: 'red',
  },
  recordButtonInner: {
    width: 64,
    height: 64,
    borderRadius: 32,
    backgroundColor: 'white',
  },
  recordingButtonInner: {
    width: 32,
    height: 32,
    borderRadius: 4,
    backgroundColor: 'red',
  },
  permissionButton: {
    backgroundColor: 'white',
    padding: 15,
    borderRadius: 8,
    marginTop: 20,
    alignSelf: 'center',
  },
  buttonText: {
    color: 'white',
    fontSize: 16,
    fontWeight: 'bold',
  },
  previewControls: {
    position: 'absolute',
    bottom: 40,
    left: 0,
    right: 0,
    alignItems: 'center',
    zIndex: 1000,
  },
  buttonRow: {
    flexDirection: 'row',
    justifyContent: 'center',
    gap: 20,
  },
  actionButton: {
    padding: 15,
    borderRadius: 8,
    minWidth: 120,
    alignItems: 'center',
  },
  retakeButton: {
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    borderWidth: 1,
    borderColor: 'white',
  },
  doneButton: {
    backgroundColor: '#007AFF',
  },
})

export default VisionCameraVideo