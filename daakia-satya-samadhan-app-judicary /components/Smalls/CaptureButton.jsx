import React from 'react';
import { TouchableOpacity, Image, Text, StyleSheet } from 'react-native';

const CaptureButton = ({ onPress , title }) => {
  return (
    <TouchableOpacity style={styles.captureButton} onPress={onPress}>
      <Image
        source={require('../../assets/images/camera_ic.png')} // Adjust the path as needed
        style={styles.imageIcon}
      />
      <Text style={styles.captureButtonText}>{title}</Text>
    </TouchableOpacity>
  );
};

const styles = StyleSheet.create({
  captureButton: {
    flexDirection: 'column',
    justifyContent: 'center',
    alignItems: 'center',
    // marginVertical: 20,
    alignSelf: 'center',
  },
  imageIcon: {
    width: 70,
    height: 70,
  },
  captureButtonText: {
    fontSize: 16,
    color: '#333',
    // marginTop: 10,
  },
});

export default CaptureButton;