import React, { useState } from 'react';
import { View, Image, TouchableOpacity, StyleSheet, Modal } from 'react-native';
import { MaterialIcons, AntDesign } from '@expo/vector-icons';
import { Video } from 'expo-av';
import PreviewComponent from '../Larges/PreviewComponent';
import PdfIcon from '../../assets/images/pdf-icon.png';

const MediaThumbnail = ({ uri, type, compressed, onDelete, thumbnailSize }) => {
  const [showPreview, setShowPreview] = useState(false);

  const handlePlayPress = () => {
    setShowPreview(true);
  };

  const handleClosePreview = () => {
    setShowPreview(false);
  };

  return (
    <>
      <View style={[styles.mediaItem, { width: thumbnailSize, height: thumbnailSize }]}>
        {type === 'video' ? (
          <TouchableOpacity 
            style={styles.videoContainer}
            onPress={handlePlayPress}
            activeOpacity={0.9}
          >
            <Video
              source={{ uri }}
              style={styles.thumbnail}
              resizeMode="cover"
              shouldPlay={false}
              isMuted={true}
              useNativeControls={false}
              posterSource={{ uri }}
              posterStyle={styles.thumbnail}
            />
            <View style={styles.playButton}>
              <MaterialIcons name="play-circle-filled" size={40} color="white" />
            </View>
          </TouchableOpacity>
        ) : type === 'pdf' ? (
          <Image source={PdfIcon} style={styles.thumbnail} resizeMode="contain" />
        ) : (
          <Image source={{ uri }} style={styles.thumbnail} resizeMode="cover" />
        )}
        
        {type === 'video' && (
          <MaterialIcons
            name="videocam"
            size={24}
            color="white"
            style={styles.mediaTypeIcon}
          />
        )}
        
        {compressed && (
          <MaterialIcons
            name="compress"
            size={20}
            color="white"
            style={[styles.mediaTypeIcon, { right: 5, left: undefined }]}
          />
        )}
        
        <TouchableOpacity style={styles.deleteButton} onPress={onDelete}>
          <AntDesign name="closecircle" size={20} color="red" />
        </TouchableOpacity>
      </View>

      <Modal
        visible={showPreview}
        transparent={true}
        animationType="slide"
        onRequestClose={handleClosePreview}
      >
        <PreviewComponent
          uri={uri}
          onClose={handleClosePreview}
        />
      </Modal>
    </>
  );
};

const styles = StyleSheet.create({
  mediaItem: {
    borderRadius: 8,
    overflow: 'hidden',
    margin: 5,
    position: 'relative',
    backgroundColor: '#F3F4F6',
    borderWidth: 1,
    borderColor: '#E5E7EB',
  },
  thumbnail: {
    width: '100%',
    height: '100%',
    resizeMode: 'cover',
  },
  videoContainer: {
    width: '100%',
    height: '100%',
    position: 'relative',
  },
  playButton: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: 'rgba(0,0,0,0.3)',
  },
  mediaTypeIcon: {
    position: 'absolute',
    bottom: 5,
    left: 5,
    backgroundColor: 'rgba(0,0,0,0.5)',
    borderRadius: 12,
    padding: 2,
  },
  deleteButton: {
    position: 'absolute',
    top: 5,
    right: 5,
    backgroundColor: 'white',
    borderRadius: 12,
  },
});

export default MediaThumbnail;