import React from 'react';
import { View, TouchableOpacity, Text, StyleSheet, Dimensions, Modal } from 'react-native';
import { Colors } from '../../constants/colors';

const { width } = Dimensions.get('window');

const OptionsOverlay = ({ visible, onSelectOption, onClose, options = [] }) => {
  return (
    <Modal
      transparent={true}
      visible={visible}
      onRequestClose={onClose} 
    >
      <View style={styles.modalContainer}>
        <View style={styles.optionsOverlay}>
          {options.map((option) => (
            <TouchableOpacity
              key={option.id}
              style={styles.optionButton}
              onPress={() => onSelectOption(option.id)}
            >
              <Text style={styles.optionText}>{option.label}</Text>
            </TouchableOpacity>
          ))}
          <TouchableOpacity
            style={styles.closeButton}
            onPress={onClose}
          >
            <Text style={styles.closeButtonText}>Close</Text>
          </TouchableOpacity>
        </View>
      </View>
    </Modal>
  );
};

const styles = StyleSheet.create({
  modalContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: 'rgba(0, 0, 0, 0.5)', // Semi-transparent background
  },
  optionsOverlay: {
    backgroundColor: Colors.background,
    borderRadius: 8,
    padding: 20,
    width: width * 0.8, // 80% of screen width
    elevation: 5,
    shadowColor: Colors.black,
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.8,
    shadowRadius: 2,
  },
  optionButton: {
    padding: 15,
    alignItems: 'center',
    borderBottomWidth: 1,
    borderBottomColor: Colors.border,
  },
  optionText: {
    fontSize: 16,
    color: Colors.black,
    fontFamily: 'Roboto',
  },
  closeButton: {
    padding: 15,
    alignItems: 'center',
    marginTop: 10,
  },
  closeButtonText: {
    fontSize: 16,
    color: Colors.primary,
    fontFamily: 'Roboto',
  },
});

export default OptionsOverlay;