export const AUTH_ERRORS = {
  INVALID_MOBILE: 'Mobile number must be exactly 10 digits.',
  INVALID_OTP: 'Please enter a valid 5-digit OTP',
  INVALID_EMAIL: 'Invalid email format',
  INVALID_NAME: 'Name must be at least 2 characters long',
  TOKEN_EXPIRED: 'Token expired or invalid',
  PROFILE_FETCH_FAILED: 'Failed to fetch user profile',
  BASE_URL_MISSING: 'Base URL is not configured.',
  INVALID_AADHAR: 'Please enter a valid 12-digit Aadhar number.',
  REGISTRATION_FAILED: 'Registration failed.',
  FETCH_COURTS_FAILED: 'Failed to fetch courts.',
  FETCH_DEPARTMENTS_FAILED: 'Failed to fetch departments.',
  FETCH_RANKS_FAILED: 'Failed to fetch police ranks.',
  UPLOAD_FAILED: 'Upload failed',
  UPLOAD_ERROR: 'Error uploading file',
};

export const STORAGE_KEYS = {
  TOKEN: 'token',
  ROLES: 'roles',
  SELECTED_ROLE: 'selectedRole',
  CATEGORY: 'category',
  USER_ID: 'userId',
  REQUEST_ID: 'requestId',
};

export const TOKEN_REFRESH_INTERVAL = 3 * 60 * 60 * 1000; // 3 hours in milliseconds

// Base endpoints
const API_BASE = '/api';
const CASE_BASE = `${API_BASE}/case`;
const USER_BASE = `${API_BASE}/user`;
const COMMON_BASE = `${API_BASE}/common`;
const FORENSIC_BASE = `${API_BASE}/forensic`;
const COURT_BASE = `${API_BASE}/court`;


export const API_ENDPOINTS = {
  // User related endpoints
  LOGIN: `${USER_BASE}/login`,
  VERIFY_OTP: `${USER_BASE}/login/verify`,
  REFRESH_TOKEN: `${USER_BASE}/refreshToken`,
  PROFILE: `${USER_BASE}/profile`,
  REGISTER: `${USER_BASE}/court/register`,


  // Court related endpoints
  COURTS: COURT_BASE,
  COURT_DEPARTMENTS: `${COURT_BASE}/:courtId/departments`,

  // Common endpoints
  POLICE_STATIONS: `${COMMON_BASE}/policeStations`,
  POLICE_DEPARTMENTS: `${COMMON_BASE}/policeDepartments`,
  POLICE_RANKS: `${COMMON_BASE}/policeRanks`,
  UPLOAD: `${API_BASE}/upload`,
  
  // Dispatcher related endpoints
  DISPATCHER_RECENT_CASES: `${CASE_BASE}/forensic/dispatch`,
  FORENSIC_REQUEST_EVIDENCES: `${FORENSIC_BASE}/request`,
  FORENSIC_REQUEST_DISPATCH: `${FORENSIC_BASE}/request`,

  // Case related endpoints
  CASE_DETAILS: CASE_BASE,
  CASE_EVIDENCE_UPDATE: CASE_BASE,
  CASE_SUBMIT_TO_FORENSIC: `${CASE_BASE}/:caseId/submitToForensic`,
  SUBMIT_EVIDENCE: CASE_BASE,
  UPDATE_CASE_PACKAGE: CASE_BASE,
  EVIDENCE_DETAILS: CASE_BASE,
  CREATE_CASE: CASE_BASE,
  UPDATE_PREMISES: CASE_BASE,
  FETCH_CASES: `${CASE_BASE}/court`,
  CASE_SEARCH: `${CASE_BASE}/search`,
  PROSECUTOR_CASES: `${CASE_BASE}/prosecutor`,

  // Forensic Reports related endpoints
  FORENSIC_REPORTS: `${FORENSIC_BASE}/report/case`,

  // Event endpoints
  EVENT: `${API_BASE}/event`,
  EVENT_BY_ID: `${API_BASE}/event/:eventId`,
  EVENT_JUDGE: `${API_BASE}/event/judge`,
  EVENT_PROSECUTOR: `${API_BASE}/event/prosecutor`,
  SEND_TO_COURT: `${COURT_BASE}/submit/case`,
  CASE_EVENTS: `${API_BASE}/event/case/:caseId`,
};

export const FILE_TYPES = {
  IMAGE: {
    extension: 'jpg',
    mimeType: 'image/jpeg',
  },
  VIDEO: {
    extension: 'mp4',
    mimeType: 'video/mp4',
  },
  PDF: {
    extension: 'pdf',
    mimeType: 'application/pdf',
  },
  DEFAULT: {
    extension: 'file',
    mimeType: 'application/octet-stream',
  },
};