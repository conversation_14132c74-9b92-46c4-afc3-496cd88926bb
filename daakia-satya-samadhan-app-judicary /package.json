{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "version": "1.0.0", "main": "expo-router/entry", "scripts": {"start": "expo start", "android": "expo run:android", "ios": "expo run:ios", "web": "expo start --web"}, "dependencies": {"@expo/vector-icons": "^14.0.2", "@react-native-async-storage/async-storage": "1.23.1", "@react-native-picker/picker": "2.9.0", "@react-navigation/drawer": "^7.1.1", "@react-navigation/native": "^7.0.14", "@react-navigation/stack": "^7.1.2", "axios": "^1.8.1", "dotenv": "^16.5.0", "expo": "~52.0.46", "expo-av": "~15.0.2", "expo-blur": "~14.0.3", "expo-calendar": "~14.0.6", "expo-camera": "~16.0.18", "expo-clipboard": "~7.0.1", "expo-constants": "~17.0.7", "expo-dev-client": "~5.0.20", "expo-document-picker": "~13.0.3", "expo-file-system": "~18.0.12", "expo-haptics": "~14.0.1", "expo-image-manipulator": "~13.0.6", "expo-image-picker": "~16.0.6", "expo-intent-launcher": "~12.0.2", "expo-linear-gradient": "~14.0.2", "expo-linking": "~7.0.5", "expo-local-authentication": "~15.0.2", "expo-location": "~18.0.10", "expo-media-library": "~17.0.6", "expo-print": "~14.0.3", "expo-router": "~4.0.21", "expo-secure-store": "~14.0.1", "expo-sharing": "~13.0.1", "expo-splash-screen": "~0.29.24", "expo-status-bar": "~2.0.1", "expo-updates": "~0.27.4", "expo-video": "~2.0.6", "expo-web-browser": "~14.0.2", "jwt-decode": "^4.0.0", "lottie-react-native": "7.1.0", "moment": "^2.30.1", "react": "18.3.1", "react-native": "0.76.9", "react-native-big-calendar": "^4.18.2", "react-native-document-scanner-plugin": "^1.0.1", "react-native-draggable-flatlist": "^4.0.3", "react-native-dropdown-select-list": "^2.0.5", "react-native-gesture-handler": "~2.20.2", "react-native-maps": "1.18.0", "react-native-pager-view": "6.5.1", "react-native-reanimated": "~3.16.1", "react-native-safe-area-context": "^4.12.0", "react-native-screens": "~4.4.0", "react-native-tab-view": "^4.0.5", "react-native-vector-icons": "^10.2.0", "react-native-vision-camera": "^4.6.4", "react-native-web": "~0.19.13", "react-native-webview": "13.12.5"}, "devDependencies": {"@babel/core": "^7.20.0"}, "private": true}