import React from 'react';
import { View, Text, StyleSheet, TouchableOpacity, Image } from 'react-native';
import { useRouter } from 'expo-router';
import { StatusBar } from 'expo-status-bar';

const NotAuthorizedScreen = () => {
  const router = useRouter();

  return (
    <View style={styles.container}>
                <StatusBar style="light" backgroundColor="#0B36A1" /> 
      {/* <Image
        source={require('../../assets/images/ic_not_authorized.png')} // Add a nice icon
        style={styles.icon}
      /> */}
      <Text style={styles.title}>Oops! 😔</Text>
      <Text style={styles.subtitle}>
        Sorry, you are not authorized to use this app.
      </Text>
      <Text style={styles.infoText}>
        If you believe this is a mistake, please contact your administrator.
      </Text>
      <TouchableOpacity
        style={styles.button}
        onPress={() => router.replace('/(auth)/welcome')}
      >
        <Text style={styles.buttonText}>Go Back to Welcome</Text>
      </TouchableOpacity>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f8f9fa', // Light background color
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: 30,
  },
  icon: {
    width: 120,
    height: 120,
    marginBottom: 20,
  },
  title: {
    fontSize: 28,
    fontWeight: 'bold',
    color: '#333',
    marginBottom: 10,
  },
  subtitle: {
    fontSize: 18,
    color: '#555',
    textAlign: 'center',
    marginBottom: 10,
    lineHeight: 24,
  },
  infoText: {
    fontSize: 14,
    color: '#777',
    textAlign: 'center',
    marginBottom: 30,
    lineHeight: 20,
  },
  button: {
    backgroundColor: '#1e90ff', // Blue button color
    paddingVertical: 15,
    paddingHorizontal: 40,
    borderRadius: 10,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.2,
    shadowRadius: 4,
    elevation: 3, // For Android shadow
  },
  buttonText: {
    color: '#fff',
    fontSize: 16,
    fontWeight: 'bold',
  },
});

export default NotAuthorizedScreen;