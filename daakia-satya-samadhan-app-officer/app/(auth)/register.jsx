import React, { useState, useEffect } from 'react';
import { 
  StyleSheet, 
  Text, 
  View, 
  ScrollView, 
  Alert, 
  Dimensions, 
  Platform,
  KeyboardAvoidingView,
  TouchableOpacity,
  ActivityIndicator,
  Keyboard,
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { StatusBar } from 'expo-status-bar';
import { router, useNavigation } from 'expo-router';
import InputField from '../../components/Smalls/InputField';
import SelectField from '../../components/Smalls/SelectField';
import ImageUploader from '../../components/Larges/ImageUploader';
import { apiService } from '../../services/api';
import { validationService } from '../../services/validation';
import { AUTH_ERRORS } from '../../constants/auth';

const { width } = Dimensions.get('window');
const isTablet = width >= 768;
const isWeb = Platform.OS === 'web';

const responsiveSize = (size) => {
  if (isWeb) {
    return size * 0.8; 
  }
  if (isTablet) {
    return size * 1.2;
  }
  return size; 
};

export default function SignUpScreen() {
  const [name, setName] = useState('');
  const [mobileNumber, setMobileNumber] = useState('');
  const [designation, setDesignation] = useState('');
  const [policeStation, setPoliceStation] = useState('');
  const [aadhar, setAadhar] = useState('');
  const [emailId, setEmailId] = useState('');
  const [department, setDepartment] = useState('');
  const [fileUrl, setFileUrl] = useState('');
  const [policeStations, setPoliceStations] = useState([]);
  const [departments, setDepartments] = useState([]);
  const [ranks, setRanks] = useState([]);
  const [isLoading, setIsLoading] = useState(false);
  const [isFetchingDepartments, setIsFetchingDepartments] = useState(false);
  const [uploadedUrl, setUploadedUrl] = useState(null);
  const [keyboardVisible, setKeyboardVisible] = useState(false);

  const handleUploadComplete = (url) => {
    setUploadedUrl(url);
    console.log('Uploaded URL:', url);
  };

  const navigation = useNavigation();

  const handleLoginPress = () => {
    navigation.navigate('(auth)/login'); 
  };

  useEffect(() => {
    fetchPoliceStations();
    fetchPoliceRanks();
    
    const keyboardDidShowListener = Keyboard.addListener(
      'keyboardDidShow',
      () => {
        setKeyboardVisible(true);
      }
    );
    const keyboardDidHideListener = Keyboard.addListener(
      'keyboardDidHide',
      () => {
        setKeyboardVisible(false);
      }
    );

    return () => {
      keyboardDidShowListener.remove();
      keyboardDidHideListener.remove();
    };
  }, []);

  const fetchPoliceStations = async () => {
    try {
      const response = await apiService.fetchPoliceStations();
      const stationOptions = response.data.map(station => ({
        key: station._id,
        value: station.name
      }));
      setPoliceStations(stationOptions);
    } catch (error) {
      Alert.alert('Error', AUTH_ERRORS.FETCH_STATIONS_FAILED);
    }
  };

  const fetchDepartments = async (stationId) => {
    if (!stationId) return;
    setIsFetchingDepartments(true);
    try {
      const response = await apiService.fetchPoliceDepartments(stationId);
      const departmentOptions = response.data.map(dept => ({
        key: dept._id,
        value: dept.name
      }));
      setDepartments(departmentOptions);
    } catch (error) {
      Alert.alert('Error', AUTH_ERRORS.FETCH_DEPARTMENTS_FAILED);
    } finally {
      setIsFetchingDepartments(false);
    }
  };

  const fetchPoliceRanks = async () => {
    try {
      const response = await apiService.fetchPoliceRanks();
      const rankOptions = response.data.map(rank => ({
        key: rank,
        value: rank
      }));
      setRanks(rankOptions);
    } catch (error) {
      Alert.alert('Error', AUTH_ERRORS.FETCH_RANKS_FAILED);
    }
  };

  const handleSignUp = async () => {
    try {
      // Validate all inputs
      const registrationData = {
        name,
        mobileNumber,
        designation,
        policeStation,
        aadhar,
        emailId,
        department,
        displayUrl: uploadedUrl,
      };

      validationService.validateRegistration(registrationData);

      setIsLoading(true);
      const response = await apiService.register(registrationData);

      if (response.status === 'success') {
        Alert.alert('Success', 'Registration successful!', [
          {
            text: 'OK',
            onPress: () => router.replace('/(auth)/thankYouPage'),
          },
        ]);
      } else {
        throw new Error(response.message || AUTH_ERRORS.REGISTRATION_FAILED);
      }
    } catch (error) {
      Alert.alert(
        'Error',
        error.message || 'An unexpected error occurred. Please try again later.'
      );
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <SafeAreaView style={styles.safeArea}>
      <StatusBar style="light" backgroundColor="#0B36A1" />
      <View style={styles.container}>
        <View style={[
          styles.headerContainer,
          keyboardVisible && { marginBottom: responsiveSize(10) }
        ]}>
          <Text style={[
            styles.header, 
            keyboardVisible && { fontSize: responsiveSize(22) }
          ]}>
            SIGN UP
          </Text>
          <Text style={styles.loginText}>
            Already have an account?{' '}
            <Text style={styles.loginLink} onPress={handleLoginPress}>
              LOG IN
            </Text>
          </Text>
        </View>
        
        <ScrollView
          contentContainerStyle={styles.scrollContainer}
          keyboardShouldPersistTaps="handled"
          showsVerticalScrollIndicator={false}
        >
          <View style={styles.formContainer}>
            <InputField
              label="Officer Name *"
              placeholder="Enter Officer Name"
              value={name}
              onChangeText={setName}
            />

            <SelectField
              label="Police Station / FSL Code *"
              data={policeStations}
              onSelect={(val) => {
                setPoliceStation(val);
                fetchDepartments(val);
              }}
              placeholder="Select Police Station"
              searchPlaceholder="Search Police Stations"
              padding={0}
            />

            <SelectField
              label="Department ID *"
              data={departments}
              onSelect={(val) => setDepartment(val)}
              placeholder="Select Department"
              searchPlaceholder="Search Departments"
              disabled={!policeStation}
              isLoading={isFetchingDepartments}
              padding={0}
            />

            <SelectField
              label="Designation *"
              data={ranks}
              onSelect={(val) => setDesignation(val)}
              placeholder="Select Designation"
              searchPlaceholder="Search Designations"
              padding={0}
            />

            <KeyboardAvoidingView
              behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
              keyboardVerticalOffset={Platform.OS === 'ios' ? 64 : 0}
              style={styles.keyboardAvoidingSection}
            >
              <InputField
                label="Aadhar No. *"
                placeholder="Enter Aadhar No."
                value={aadhar}
                onChangeText={setAadhar}
                keyboardType="numeric"
                maxLength={12}
              />

              <InputField
                label="Mobile No. *"
                placeholder="Enter Mobile No."
                value={mobileNumber}
                onChangeText={setMobileNumber}
                keyboardType="numeric"
                maxLength={10}
                prefix="+91"
              />

              <InputField
                label="Govt. Email ID *"
                placeholder="Enter Govt. Email ID"
                value={emailId}
                onChangeText={setEmailId}
                keyboardType="email-address"
              />
            </KeyboardAvoidingView>

            <ImageUploader onUploadComplete={handleUploadComplete} />

            <TouchableOpacity
              style={[styles.signUpButton, isLoading && styles.disabledButton]}
              onPress={handleSignUp}
              disabled={isLoading}
            >
              {isLoading ? (
                <ActivityIndicator color="#fff" />
              ) : (
                <Text style={styles.signUpButtonText}>Sign Up</Text>
              )}
            </TouchableOpacity>
          </View>
        </ScrollView>
      </View>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  safeArea: {
    flex: 1,
    backgroundColor: '#fff',
  },
  container: {
    flex: 1,
  },
  headerContainer: {
    paddingHorizontal: responsiveSize(20),
    paddingTop: responsiveSize(10),
  },
  scrollContainer: {
    flexGrow: 1,
    paddingHorizontal: responsiveSize(20),
    paddingBottom: responsiveSize(20),
  },
  formContainer: {
    width: '100%',
  },
  header: {
    fontSize: responsiveSize(28),
    fontWeight: 'bold',
    marginBottom: responsiveSize(10),
    color: '#0B36A1',
    textAlign: 'center',
  },
  loginText: {
    fontSize: responsiveSize(16),
    marginBottom: responsiveSize(20),
    textAlign: 'center',
    color: '#c4c4c4c',
  },
  loginLink: {
    color: '#0647a1',
    fontWeight: 'bold',
  },
  keyboardAvoidingSection: {
    width: '100%',
  },
  signUpButton: {
    backgroundColor: '#0B36A1',
    padding: responsiveSize(15),
    borderRadius: 10,
    alignItems: 'center',
    marginTop: responsiveSize(20),
    width: isWeb ? '50%' : '100%',
    alignSelf: 'center',
  },
  disabledButton: {
    backgroundColor: '#c4c4c4c',
  },
  signUpButtonText: {
    color: 'white',
    fontSize: responsiveSize(18),
    fontWeight: 'bold',
  },
});