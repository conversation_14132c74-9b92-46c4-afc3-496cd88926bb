import React, { useEffect, useRef } from 'react';
import { View, Text, StyleSheet, StatusBar, Platform, Dimensions } from 'react-native';
import { useRouter } from 'expo-router';
import LottieView from 'lottie-react-native';

const { width, height } = Dimensions.get('window');

const ThankYouPage = () => {
  const router = useRouter();
  const animationRef = useRef(null);

  useEffect(() => {
    // Start the animation immediately
    if (animationRef.current) {
      animationRef.current.play();
    }

    // Redirect after animation
    const redirectTimer = setTimeout(() => {
      router.replace('/(auth)/welcome');
    }, 4000);

    return () => clearTimeout(redirectTimer);
  }, []);

  return (
    <View style={styles.overlay}>
      <StatusBar style="light" backgroundColor="#0B36A1" />
      <View style={styles.container}>
        <View style={styles.content}>
          <View style={styles.iconWrapper}>
            <LottieView
              ref={animationRef}
              source={require('../../assets/animations/success.json')}
              style={styles.lottie}
              autoPlay
              loop={false}
            />
          </View>
          <Text style={styles.title}>Thank You!</Text>
          <Text style={styles.subtitle}>
            Thank you for your quick response. We have received your submission.
          </Text>
          <Text style={styles.infoText}>
            Your profile is being verified by the super admin and will be activated within 24 hours.
          </Text>
        </View>
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  overlay: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    backgroundColor: 'rgba(255, 255, 255, 0.98)',
    zIndex: 9999,
  },
  container: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 20,
    ...Platform.select({
      ios: {
        paddingTop: 50,
      },
      android: {
        paddingTop: StatusBar.currentHeight || 0,
      },
    }),
  },
  content: {
    alignItems: 'center',
    justifyContent: 'center',
    width: '90%',
    maxWidth: 340,
  },
  iconWrapper: {
    marginBottom: 20,
    height: 100,
    width: 100,
    justifyContent: 'center',
    alignItems: 'center',
  },
  lottie: {
    width: 100,
    height: 100,
  },
  title: {
    fontSize: 22,
    fontFamily: 'Roboto_bold',
    color: '#333',
    textAlign: 'center',
    lineHeight: 28,
    marginBottom: height * 0.02,
  },
  subtitle: {
    fontSize: 18,
    fontFamily: 'Roboto',
    color: '#4A5568',
    textAlign: 'center',
    marginBottom: height * 0.02,
    lineHeight: 24,
  },
  infoText: {
    fontSize: 16,
    fontFamily: 'Roboto',
    color: '#1a73e8',
    textAlign: 'center',
    marginTop: height * 0.02,
    lineHeight: 22,
  },
});

export default ThankYouPage;