import { View, Image, TouchableOpacity, Text, StyleSheet, Dimensions } from 'react-native';
import { useRouter } from 'expo-router';
import { StatusBar } from 'expo-status-bar';

const { width } = Dimensions.get('window');
const isTablet = width >= 768;
const isWindows = width > 1068;

export default function Welcome() {
  const router = useRouter();

  return (
    <View style={styles.container}>
                  <StatusBar style="light" backgroundColor="#0B36A1" /> 
      {/* Logo */}
      <Image source={require('../../assets/images/satya_samadhan_logo.png')} style={styles.logo} />

      {/* Buttons */}
      <TouchableOpacity style={styles.loginButton} onPress={() => router.replace('/login')}>
        <Text style={styles.loginText}>USER LOGIN</Text>
      </TouchableOpacity>

      <TouchableOpacity style={styles.signupButton} onPress={() => router.push('/register')}>
        <Text style={styles.signupText}>SIGN UP</Text>
      </TouchableOpacity>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: '#fff',
    paddingHorizontal: isWindows ? 100 : isTablet ? 50 : 20, 
    fontFamily: 'Roboto',
  },
  logo: {
    width: isWindows ? 300 : isTablet ? 350 : 250, 
    height: isWindows ? 300 : isTablet ? 350 : 250,
    resizeMode: 'contain',
    marginBottom: isWindows ? 40 : isTablet ? 40 : 30,
  },
  loginButton: {
    backgroundColor: '#0B36A1',
    paddingVertical: isWindows ? 13 : isTablet ? 15 : 12,
    paddingHorizontal: isWindows ? 40 : isTablet ? 60 : 50,
    borderRadius: 25,
    marginBottom: 15,
    width: isWindows ? 400 : isTablet ? 300 : 250, // Responsive button width
    alignItems: 'center',
    fontFamily: 'Roboto',
  },
  loginText: {
    color: '#fff',
    fontSize: isWindows ? 20 : isTablet ? 18 : 16,
    fontWeight: 'bold',
    fontFamily: 'Roboto',
  },
  signupButton: {
    borderColor: '#0B36A1',
    borderWidth: 2,
    paddingVertical: isWindows ? 18 : isTablet ? 15 : 12,
    paddingHorizontal: isWindows ? 80 : isTablet ? 60 : 50,
    borderRadius: 25,
    width: isWindows ? 400 : isTablet ? 300 : 250,
    alignItems: 'center',
    fontFamily: 'Roboto',
  },
  signupText: {
    color: '#0B36A1',
    fontSize: isWindows ? 20 : isTablet ? 18 : 16,
    fontWeight: 'bold',
    fontFamily: 'Roboto',
  },
});
