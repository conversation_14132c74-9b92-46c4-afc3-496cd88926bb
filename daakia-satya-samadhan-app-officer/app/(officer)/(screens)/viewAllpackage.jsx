import React, { useState, useEffect } from 'react';
import { 
  View, 
  Text, 
  StyleSheet, 
  Image, 
  TouchableOpacity, 
  ScrollView,
  Dimensions,
  StatusBar,
  SafeAreaView,
  ActivityIndicator,
  Modal,
  FlatList,
  Platform,
  useWindowDimensions,
} from 'react-native';
import { useLocalSearchParams, router } from 'expo-router';
import { Ionicons, MaterialIcons } from '@expo/vector-icons';
import PreviewComponent from '../../../components/Larges/PreviewComponent';
import SuccessScreen from '../../../components/Smalls/SuccessScreen';
import LocationViewer from '../../../components/Larges/LocationViewer';
import VideoPlayer from '../../../components/Larges/VideoPlayer';
import TagInfoModal from '../../../components/Larges/TagInfoModal';
import AdviceInfoModal from '../../../components/Larges/AdviceInfoModal';
import { transformUrl } from '../../../utils/transformUrl';
import { useAuth } from '../../../context/auth-context';
import { apiService } from '../../../services/api';

const { width } = Dimensions.get('window');
const THUMBNAIL_SIZE = width / 2 - 30; 

const ViewAllEvidences = () => {
  const { caseid, evidenceId, packageImageUrls } = useLocalSearchParams();
  const [evidenceData, setEvidenceData] = useState(null);
  const [showSuccessScreen, setShowSuccessScreen] = useState(false);
  const [loadingImages, setLoadingImages] = useState({});
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState(null);
  const [showLocationViewer, setShowLocationViewer] = useState(false);
  const [currentIndex, setCurrentIndex] = useState(0);
  const [previewUri, setPreviewUri] = useState(null);
  const [showAdviceInfo, setShowAdviceInfo] = useState(false);
  const [showTagModal, setShowTagModal] = useState(false);
  const [selectedTagInfo, setSelectedTagInfo] = useState(null);
  const { token } = useAuth();
  const { width: screenWidth } = useWindowDimensions();

  // Helper functions to extract coordinates from GPS location string
  const extractLatitude = (gpsLocation) => {
    if (!gpsLocation) return null;
    const match = gpsLocation.match(/Latitude:\s*([\d.-]+)/);
    return match ? parseFloat(match[1]) : null;
  };

  const extractLongitude = (gpsLocation) => {
    if (!gpsLocation) return null;
    const match = gpsLocation.match(/Longitude:\s*([\d.-]+)/);
    return match ? parseFloat(match[1]) : null;
  };

  // Fetch evidence details to get package images
  useEffect(() => {
    fetchEvidenceDetails();
  }, [caseid, evidenceId]);

  const fetchEvidenceDetails = async () => {
    if (!caseid || !evidenceId) {
      console.log('Missing required info:', { caseid, evidenceId });
      setError('Missing required information');
      setIsLoading(false);
      return;
    }

    try {
      const result = await apiService.fetchEvidenceDetails(token, caseid, evidenceId);
      console.log('Evidence details:', JSON.stringify(result, null, 2));
      
      if (result.data) {
        setEvidenceData(result.data);
      }
      setError(null);
    } catch (error) {
      console.error('Error fetching evidence details:', error);
      setError('Failed to load evidence details. Please try again.');
    } finally {
      setIsLoading(false);
    }
  };

  const handleGoBack = () => {
    router.back();
  };

  // const handleAddNew = () => {
  //   router.navigate({
  //     pathname: '(screens)/capturePackage',
  //     params: { caseid, evidenceId },
  //   });
  // };

  const handleSubmit = () => {
    // Show the SuccessScreen
    setShowSuccessScreen(true);

  
    setTimeout(() => {
      setShowSuccessScreen(false);
      router.replace({
        pathname: '(screens)/caseDetails', 
        params: { caseid },
      });
    }, 3000);
  };

  const handleMediaPress = (mediaUrl) => {
    setPreviewUri(mediaUrl);
  };

  // Utility function to check if file is video (same as evidenceDetails.jsx)
  const isVideoFile = (url) => {
    if (!url) return false;
    const lowerCaseUrl = url.toLowerCase();
    return lowerCaseUrl.endsWith('.mp4') || 
           lowerCaseUrl.endsWith('.mov') || 
           lowerCaseUrl.endsWith('.avi') || 
           lowerCaseUrl.endsWith('.mkv');
  };

  // Format date function
  const formatDate = (dateString) => {
    if (!dateString) return 'N/A';
    return new Date(dateString).toLocaleString();
  };

  // Format duration function
  const formatDuration = (minutes) => {
    const hours = Math.floor(minutes / 60);
    const remainingMinutes = minutes % 60;
    if (hours > 0) {
      return `${hours} hour${hours > 1 ? 's' : ''} ${remainingMinutes > 0 ? `and ${remainingMinutes} minute${remainingMinutes > 1 ? 's' : ''}` : ''}`;
    }
    return `${minutes} minute${minutes > 1 ? 's' : ''}`;
  };

  // Show tag information modal
  const showTagInfo = (tag) => {
    setSelectedTagInfo(tag);
    setShowTagModal(true);
  };

  // Function to render package images in horizontal scroll like evidenceDetails.jsx
  const renderPackageImages = () => {
    if (!evidenceData?.packagingUrl || evidenceData.packagingUrl.length === 0) {
      return null;
    }

    return (
      <View style={styles.sectionContainer}>
        <Text style={styles.sectionLabel}>Package Photos</Text>
        <FlatList
          data={evidenceData.packagingUrl}
          renderItem={({ item }) => {
            const mediaUrl = transformUrl(item);
            const videoStyles = { width: screenWidth - 32, height: 240 };

            return (
              <TouchableOpacity
                onPress={() => setPreviewUri(mediaUrl)}
              >
                {isVideoFile(item) ? (
                  <VideoPlayer uri={mediaUrl} style={videoStyles} />
                ) : (
                  <Image
                    source={{ uri: mediaUrl }}
                    style={[styles.packageMedia, { width: screenWidth - 32 }]}
                    resizeMode="cover"
                  />
                )}
              </TouchableOpacity>
            );
          }}
          keyExtractor={(_, index) => `package-${index}`}
          horizontal
          pagingEnabled
          showsHorizontalScrollIndicator={false}
          snapToInterval={screenWidth - 32}
          decelerationRate="fast"
          onScroll={(event) => {
            const offset = event.nativeEvent.contentOffset.x;
            const index = Math.round(offset / (screenWidth - 32));
            setCurrentIndex(index);
          }}
          contentContainerStyle={{ paddingHorizontal: 0 }}
        />

        <View style={styles.swipeIndicatorContainer}>
          {evidenceData.packagingUrl.map((_, index) => (
            <View
              key={index}
              style={[
                styles.swipeIndicator,
                index === currentIndex && styles.activeSwipeIndicator,
              ]}
            />
          ))}
        </View>
      </View>
    );
  };

  // Function to render attachment images in responsive grid
  const renderAttachmentGrid = () => {
    if (!evidenceData?.attachmentUrl || evidenceData.attachmentUrl.length === 0) {
      return null;
    }

    // Calculate responsive grid columns based on screen width
    const screenWidth = Dimensions.get('window').width;
    let numColumns = 2; // Default for phones
    
    if (screenWidth > 768) {
      numColumns = 4; // Tablets
    } else if (screenWidth > 480) {
      numColumns = 3; // Large phones
    }

    const itemWidth = (screenWidth - 48 - (numColumns - 1) * 8) / numColumns; // 48 = padding, 8 = gap

    return (
      <View style={styles.attachmentGrid}>
          {evidenceData.attachmentUrl.map((item, index) => {
            const mediaUrl = transformUrl(item);
            const isVideo = isVideoFile(item);
            const isLoading = loadingImages[mediaUrl] !== false;

            return (
              <TouchableOpacity
                key={`attachment-${index}`}
                style={[styles.attachmentItem, { width: itemWidth }]}
                onPress={() => handleMediaPress(mediaUrl)}
              >
                {isVideo ? (
                  <View style={[styles.attachmentVideoPlaceholder, { width: itemWidth, height: itemWidth }]}>
                    <Ionicons name="videocam" size={24} color="#0B36A1" />
                    <Text style={styles.attachmentVideoText}>Video</Text>
                  </View>
                ) : (
                  <View style={[styles.attachmentImageContainer, { width: itemWidth, height: itemWidth }]}>
                    {isLoading && (
                      <View style={styles.attachmentLoadingContainer}>
                        <ActivityIndicator size="small" color="#0B36A1" />
                      </View>
                    )}
                    <Image
                      source={{ uri: mediaUrl }}
                      style={styles.attachmentImage}
                      resizeMode="cover"
                      onLoadStart={() => setLoadingImages(prev => ({ ...prev, [mediaUrl]: true }))}
                      onLoadEnd={() => setLoadingImages(prev => ({ ...prev, [mediaUrl]: false }))}
                    />
                  </View>
                )}
              </TouchableOpacity>
            );
          })}
        </View>
    );
  };

  if (isLoading) {
    return (
      <SafeAreaView style={styles.safeAreaContainer}>
        <StatusBar style='dark' />
        <View style={styles.container}>
          <View style={styles.centerContainer}>
            <ActivityIndicator size="large" color="#0B36A1" />
          </View>
        </View>
      </SafeAreaView>
    );
  }

  if (error) {
    return (
      <SafeAreaView style={styles.safeAreaContainer}>
        <StatusBar style='dark' />
        <View style={styles.container}>
          <View style={styles.centerContainer}>
            <Text style={styles.errorText}>{error}</Text>
            <TouchableOpacity onPress={fetchEvidenceDetails} style={styles.retryButton}>
              <Text style={styles.retryButtonText}>Retry</Text>
            </TouchableOpacity>
          </View>
        </View>
      </SafeAreaView>
    );
  }

  return (
    <SafeAreaView style={styles.safeAreaContainer}>
      <StatusBar style='dark' />
      
      <View style={styles.container}>
        {evidenceData ? (
          <ScrollView 
            contentContainerStyle={styles.scrollContent}
            style={styles.scrollView}
          >
            {/* Evidence Details */}
            <View style={styles.detailsSection}>
              <Text style={styles.sectionLabel}>Evidence Information</Text>
              <View style={styles.detailsCard}>
                {evidenceData.type && (
                  <View style={styles.detailRow}>
                    <Text style={styles.detailLabel}>Type:</Text>
                    <Text style={styles.detailValue}>{evidenceData.type}</Text>
                  </View>
                )}

                {evidenceData.title && (
                  <View style={styles.detailRow}>
                    <Text style={styles.detailLabel}>Title:</Text>
                    <Text style={styles.detailValue}>{evidenceData.title}</Text>
                  </View>
                )}

                {evidenceData.description && (
                  <View style={styles.detailRow}>
                    <Text style={styles.detailLabel}>Description:</Text>
                    <Text style={styles.detailValue}>{evidenceData.description}</Text>
                  </View>
                )}

                {evidenceData.time && (
                  <>
                    <View style={styles.detailRow}>
                      <Text style={styles.detailLabel}>Date:</Text>
                      <Text style={styles.detailValue}>{new Date(evidenceData.time).toLocaleDateString()}</Text>
                    </View>
                    <View style={styles.detailRow}>
                      <Text style={styles.detailLabel}>Time:</Text>
                      <Text style={styles.detailValue}>{new Date(evidenceData.time).toLocaleTimeString()}</Text>
                    </View>
                  </>
                )}



                {evidenceData.gpsLocation && (
                  <View style={styles.detailRow}>
                    <Text style={styles.detailLabel}>Location:</Text>
                    <TouchableOpacity
                      style={styles.locationButton}
                      onPress={() => setShowLocationViewer(true)}
                    >
                      <MaterialIcons name="location-on" size={20} color="#0B36A1" />
                      <Text style={styles.locationText}>View on Map</Text>
                    </TouchableOpacity>
                  </View>
                )}

                {evidenceData.tags && evidenceData.tags.length > 0 && (
                  <View style={styles.detailRow}>
                    <View style={styles.labelContainer}>
                      <Text style={styles.detailLabel}>Advice:</Text>
                      <TouchableOpacity
                        style={styles.infoButton}
                        onPress={() => setShowAdviceInfo(true)}
                      >
                        <Ionicons name="information-circle-outline" size={16} color="#0B36A1" />
                      </TouchableOpacity>
                    </View>
                    <View style={styles.tagTitlesContainer}>
                      {evidenceData.tags.map((tag, index) => (
                        <TouchableOpacity
                          key={tag._id || index}
                          style={styles.tagTitleButton}
                          onPress={() => showTagInfo(tag)}
                        >
                          <Text style={styles.tagTitleText}>
                            {tag.type.split('_').map(word => 
                              word.charAt(0).toUpperCase() + word.slice(1)
                            ).join(' ')}
                          </Text>
                        </TouchableOpacity>
                      ))}
                    </View>
                  </View>
                )}
              </View>
            </View>
            {renderPackageImages()}
            <View style={styles.attachmentSection}>
              <Text style={styles.sectionLabel}>Evidence Photos</Text>
              {renderAttachmentGrid()}
            </View>
          </ScrollView>
        ) : (
          <View style={styles.emptyContainer}>
            <Ionicons name="images-outline" size={60} color="#0B36A1" />
            <Text style={styles.emptyText}>No evidence data available</Text>
            {/* <TouchableOpacity 
              style={styles.addFirstButton}
              onPress={handleAddNew}
            >
              <Text style={styles.addFirstButtonText}>Add First Evidence</Text>
            </TouchableOpacity> */}
          </View>
        )}

        {/* Submit Button at the bottom */}
        {evidenceData && (
          <View style={styles.buttonContainer}>
            <TouchableOpacity 
              style={styles.submitButton}
              onPress={handleSubmit}
            >
              <Text style={styles.submitButtonText}>SYNC EVIDENCE </Text>
            </TouchableOpacity>
          </View>
        )}

        {/* Location Viewer */}
        <LocationViewer
          isVisible={showLocationViewer}
          onClose={() => setShowLocationViewer(false)}
          latitude={evidenceData?.gpsLocation ? extractLatitude(evidenceData.gpsLocation) : null}
          longitude={evidenceData?.gpsLocation ? extractLongitude(evidenceData.gpsLocation) : null}
          title="Evidence Location"
        />

        {/* Preview Modal */}
        <Modal
          visible={!!previewUri}
          transparent={true}
          animationType="slide"
          onRequestClose={() => setPreviewUri(null)}
        >
          <PreviewComponent
            uri={previewUri}
            onClose={() => setPreviewUri(null)}
          />
        </Modal>



        {/* Tag Information Modal */}
        <TagInfoModal
          visible={showTagModal}
          onClose={() => setShowTagModal(false)}
          tagInfo={selectedTagInfo}
          formatDuration={formatDuration}
        />

        {/* Advice Info Modal */}
        <AdviceInfoModal
          visible={showAdviceInfo}
          onClose={() => setShowAdviceInfo(false)}
        />

        {/* Render SuccessScreen conditionally */}
        {showSuccessScreen && (
          <SuccessScreen 
            message="Evidence Package data synced successfully!"
            duration={3000}
            onComplete={() => setShowSuccessScreen(true)}
          />
        )}
      </View>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  safeAreaContainer: {
    flex: 1,
    backgroundColor: '#0B36A1', // Top background color for status bar
  },
  container: {
    flex: 1,
    backgroundColor: '#f8f9fa',
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    backgroundColor: '#0B36A1',
    padding: 16,
  },
  backButton: {
    padding: 4,
  },
  headerTitle: {
    color: '#fff',
    fontSize: 18,
    fontWeight: 'bold',
  },
  headerRight: {
    width: 24,
  },
  scrollView: {
    flex: 1,
  },
  scrollContent: {
    padding: 16,
    paddingBottom: 100, // Extra padding for submit button
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: '#0B36A1',
    marginBottom: 12,
    paddingHorizontal: 16,
  },
  row: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: 20,
  },
  evidenceItem: {
    width: THUMBNAIL_SIZE,
    height: THUMBNAIL_SIZE + 50,
    borderRadius: 10,
    backgroundColor: '#fff',
    overflow: 'hidden',
    elevation: 3,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
  },
  evidenceImage: {
    width: '100%',
    height: THUMBNAIL_SIZE,
  },
  evidenceLabelContainer: {
    padding: 8,
    backgroundColor: '#fff',
  },
  evidenceLabel: {
    fontSize: 14,
    fontWeight: '500',
    color: '#333',
  },
  evidenceFormat: {
    fontSize: 12,
    color: '#666',
    marginTop: 2,
  },
  videoPlaceholder: {
    width: '100%',
    height: THUMBNAIL_SIZE,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: '#f0f7ff',
  },
  videoText: {
    fontSize: 14,
    color: '#0B36A1',
    marginTop: 8,
  },
  addNewButton: {
    width: THUMBNAIL_SIZE,
    height: THUMBNAIL_SIZE,
    borderRadius: 10,
    borderWidth: 2,
    borderColor: '#0B36A1',
    borderStyle: 'dashed',
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: 'rgba(10, 52, 161, 0.05)',
  },
  addButtonIcon: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: '#0B36A1',
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: 8,
  },
  addNewText: {
    color: '#0B36A1',
    fontSize: 14,
    fontWeight: '500',
    textAlign: 'center',
    paddingHorizontal: 10,
  },
  emptySpace: {
    width: THUMBNAIL_SIZE,
  },
  buttonContainer: {
    position: 'absolute',
    left: 0,
    right: 0,
    bottom: 0,
    backgroundColor: '#fff',
    padding: 16,
    borderTopWidth: 1,
    borderTopColor: '#e0e0e0',
  },
  submitButton: {
    backgroundColor: '#0B36A1',
    padding: 16,
    borderRadius: 50,
    alignItems: 'center',
  },
  submitButtonText: {
    color: 'white',
    fontSize: 16,
    fontWeight: '600',
  },
  previewContainer: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    zIndex: 2000, 
    elevation: 10, 
    backgroundColor: 'rgba(0,0,0,0.9)',
  },
  centerContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 20,
  },
  errorText: {
    color: '#EB5757',
    marginBottom: 16,
    textAlign: 'center',
    fontSize: 16,
  },
  retryButton: {
    backgroundColor: '#0B36A1',
    paddingVertical: 12,
    paddingHorizontal: 24,
    borderRadius: 8,
  },
  retryButtonText: {
    color: '#FFFFFF',
    fontWeight: '500',
  },
  emptyContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 20,
  },
  emptyText: {
    fontSize: 18,
    color: '#666',
    marginTop: 16,
    marginBottom: 20,
  },
  addFirstButton: {
    backgroundColor: '#0B36A1',
    paddingVertical: 12,
    paddingHorizontal: 24,
    borderRadius: 8,
    marginTop: 10,
  },
  addFirstButtonText: {
    color: '#fff',
    fontSize: 16,
    fontWeight: '500',
  },
  imageContainer: {
    width: '100%',
    height: THUMBNAIL_SIZE,
    position: 'relative',
  },
  loadingContainer: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: '#f0f0f0',
  },
  // Modal styles
  modalContainer: {
    flex: 1,
    backgroundColor: '#fff',
  },
  modalHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: 20,
    paddingTop: Platform.OS === 'ios' ? 50 : 20,
    paddingBottom: 20,
    borderBottomWidth: 1,
    borderBottomColor: '#e0e0e0',
    backgroundColor: '#fff',
  },
  modalTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#333',
  },
  closeButton: {
    padding: 8,
  },
  modalContent: {
    flex: 1,
    padding: 16,
  },
  mediaSection: {
    marginBottom: 20,
  },
  mediaListContainer: {
    paddingHorizontal: 16,
  },
  evidenceMedia: {
    width: Dimensions.get('window').width - 64,
    height: 240,
    borderRadius: 8,
    marginRight: 16,
  },
  infoSection: {
    marginBottom: 20,
  },
  infoCard: {
    backgroundColor: '#f8f9fa',
    borderRadius: 12,
    padding: 16,
    borderWidth: 1,
    borderColor: '#e0e0e0',
  },
  infoRow: {
    flexDirection: 'row',
    paddingVertical: 10,
    borderBottomWidth: 1,
    borderBottomColor: '#f0f0f0',
  },
  infoLabel: {
    width: '40%',
    fontSize: 14,
    fontWeight: '500',
    color: '#555',
  },
  infoValue: {
    flex: 1,
    fontSize: 14,
    color: '#333',
  },
  locationButton: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#e3f2fd',
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 16,
  },
  locationText: {
    color: '#0B36A1',
    fontSize: 14,
    fontWeight: '500',
    marginLeft: 4,
  },
  // New styles for main page layout
  packageImageContainer: {
    marginBottom: 20,
    borderRadius: 12,
    overflow: 'hidden',
    backgroundColor: '#fff',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
    borderWidth: 2,
    borderColor: '#0B36A1',
    padding: 8,
  },
  packageImage: {
    width: '100%',
    height: 300,
    borderRadius: 8,
  },
  detailsSection: {
    marginBottom: 20,
  },
  detailsCard: {
    backgroundColor: '#fff',
    borderRadius: 12,
    padding: 16,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 0,
  },
  detailRow: {
    flexDirection: 'row',
    paddingVertical: 10,
  },
  detailLabel: {
    width: '40%',
    fontSize: 14,
    fontWeight: '500',
    color: '#555',
  },
  detailValue: {
    flex: 1,
    fontSize: 14,
    color: '#333',
  },
  attachmentSection: {
    marginBottom: 20,
  },
  attachmentGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    justifyContent: 'space-between',
    gap: 8,
  },
  attachmentItem: {
    marginBottom: 8,
    borderRadius: 8,
    overflow: 'hidden',
    backgroundColor: '#fff',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
    elevation: 2,
  },
  attachmentImageContainer: {
    position: 'relative',
  },
  attachmentImage: {
    width: '100%',
    height: '100%',
  },
  attachmentVideoPlaceholder: {
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: '#f0f7ff',
  },
  attachmentVideoText: {
    fontSize: 12,
    color: '#0B36A1',
    marginTop: 4,
  },
  attachmentLoadingContainer: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: '#f0f0f0',
  },
  // New styles for package images like evidenceDetails.jsx
  sectionContainer: {
    marginBottom: 20,
    paddingTop: 12,
  },
  sectionLabel: {
    fontSize: 18,
    fontWeight: '600',
    color: '#0B36A1',
    marginBottom: 12,
    paddingHorizontal: 6,
  },
  packageMedia: {
    height: 240,
    borderRadius: 8,
  },
  swipeIndicatorContainer: {
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'center',
    marginTop: 12,
  },
  swipeIndicator: {
    width: 8,
    height: 8,
    borderRadius: 4,
    backgroundColor: '#ccc',
    marginHorizontal: 4,
  },
  activeSwipeIndicator: {
    backgroundColor: '#0B36A1',
    width: 10,
    height: 10,
    borderRadius: 5,
  },

  bulletPointContainer: {
    flexDirection: 'row',
    marginBottom: 8,
    paddingRight: 8,
  },
  bulletPoint: {
    fontSize: 14,
    color: '#333',
    marginRight: 8,
    width: 8,
  },
  // Advice section styles
  labelContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    width: '40%',
  },
  infoButton: {
    marginLeft: 8,
    padding: 4,
  },
  // Tag titles styles
  tagTitlesContainer: {
    flex: 1,
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: 8,
  },
  tagTitleButton: {
    backgroundColor: '#f0f7ff',
    borderWidth: 1,
    borderColor: '#0B36A1',
    borderRadius: 16,
    paddingHorizontal: 12,
    paddingVertical: 6,
  },
  tagTitleText: {
    fontSize: 14,
    color: '#0B36A1',
    fontWeight: '500',
  },
});

export default ViewAllEvidences;