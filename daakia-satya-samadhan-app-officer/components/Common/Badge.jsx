import React from 'react';
import { View, Text, StyleSheet } from 'react-native';
import { Colors } from '../../constants/colors';

const Badge = ({ 
  text, 
  type = 'default', // default, success, warning, error
  size = 'medium', // small, medium, large
  style 
}) => {
  const getBadgeColor = () => {
    switch (type) {
      case 'success':
        return '#4CAF50';
      case 'warning':
        return '#FF9800';
      case 'error':
        return '#F44336';
      default:
        return Colors.primary;
    }
  };

  const getBadgeSize = () => {
    switch (size) {
      case 'small':
        return {
          paddingVertical: 1,
          paddingHorizontal: 5,
          fontSize: 9,
          borderRadius: 20,
        };
      case 'large':
        return {
          paddingVertical: 4,
          paddingHorizontal: 8,
          fontSize: 12,
          borderRadius: 6,
        };
      default: // medium
        return {
          paddingVertical: 2,
          paddingHorizontal: 6,
          fontSize: 10,
          borderRadius: 5,
        };
    }
  };

  return (
    <View style={[
      styles.badge,
      { backgroundColor: getBadgeColor() },
      { ...getBadgeSize() },
      style
    ]}>
      <Text style={[styles.text, { fontSize: getBadgeSize().fontSize }]}>{text}</Text>
    </View>
  );
};

const styles = StyleSheet.create({
  badge: {
    alignSelf: 'flex-start',
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 1,
    },
    shadowOpacity: 0.2,
    shadowRadius: 1.41,
    elevation: 2,
  },
  text: {
    color: '#FFFFFF',
    fontWeight: '700',
    textAlign: 'center',
    fontFamily: 'Roboto',
    textTransform: 'uppercase',
  },
});

export default Badge; 