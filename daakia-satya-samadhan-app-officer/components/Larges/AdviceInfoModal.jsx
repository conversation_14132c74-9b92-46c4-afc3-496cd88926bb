import React from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  ScrollView,
  Modal,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';

const AdviceInfoModal = ({ visible, onClose }) => {
  return (
    <Modal
      visible={visible}
      transparent={true}
      animationType="fade"
      onRequestClose={onClose}
    >
      <View style={styles.adviceModalOverlay}>
        <View style={styles.adviceModalContent}>
          <View style={styles.adviceModalHeader}>
            <Text style={styles.adviceModalTitle}>What is Evidence Advice?</Text>
            <TouchableOpacity onPress={onClose}>
              <Ionicons name="close" size={24} color="#333" />
            </TouchableOpacity>
          </View>
          <ScrollView showsVerticalScrollIndicator={false} style={styles.adviceModalBody}>
            <View style={styles.adviceModalSection}>
              <Text style={styles.adviceModalText}>
                Evidence Advice provides specialized guidance for handling different types of evidence based on their characteristics and requirements.
              </Text>
            </View>
            
            <View style={styles.adviceModalSection}>
              <Text style={styles.adviceModalSectionTitle}>What it includes:</Text>
              <View style={styles.adviceModalDetails}>
                <View style={styles.bulletPointContainer}>
                  <Text style={styles.bulletPoint}>•</Text>
                  <Text style={styles.adviceModalText}>
                    <Text style={styles.boldText}>Submission Time:</Text> How quickly the evidence must be submitted to maintain its validity
                  </Text>
                </View>
                <View style={styles.bulletPointContainer}>
                  <Text style={styles.bulletPoint}>•</Text>
                  <Text style={styles.adviceModalText}>
                    <Text style={styles.boldText}>Packaging Requirements:</Text> Specific container types, sizes, materials, and quantities needed
                  </Text>
                </View>
                <View style={styles.bulletPointContainer}>
                  <Text style={styles.bulletPoint}>•</Text>
                  <Text style={styles.adviceModalText}>
                    <Text style={styles.boldText}>Handling Instructions:</Text> Step-by-step guidance for proper packaging and preservation
                  </Text>
                </View>
                <View style={styles.bulletPointContainer}>
                  <Text style={styles.bulletPoint}>•</Text>
                  <Text style={styles.adviceModalText}>
                    <Text style={styles.boldText}>Best Practices:</Text> Recommendations to ensure evidence integrity and admissibility
                  </Text>
                </View>
              </View>
            </View>

            <View style={styles.adviceModalSection}>
              <Text style={styles.adviceModalSectionTitle}>Why it's important:</Text>
              <View style={styles.adviceModalDetails}>
                <View style={styles.bulletPointContainer}>
                  <Text style={styles.bulletPoint}>•</Text>
                  <Text style={styles.adviceModalText}>
                    Ensures evidence maintains its forensic value
                  </Text>
                </View>
                <View style={styles.bulletPointContainer}>
                  <Text style={styles.bulletPoint}>•</Text>
                  <Text style={styles.adviceModalText}>
                    Prevents contamination and degradation
                  </Text>
                </View>
                <View style={styles.bulletPointContainer}>
                  <Text style={styles.bulletPoint}>•</Text>
                  <Text style={styles.adviceModalText}>
                    Helps meet legal and procedural requirements
                  </Text>
                </View>
                <View style={styles.bulletPointContainer}>
                  <Text style={styles.bulletPoint}>•</Text>
                  <Text style={styles.adviceModalText}>
                    Improves the chances of successful analysis
                  </Text>
                </View>
              </View>
            </View>
          </ScrollView>
        </View>
      </View>
    </Modal>
  );
};

const styles = StyleSheet.create({
  adviceModalOverlay: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    justifyContent: 'center',
    alignItems: 'center',
    zIndex: 1000,
  },
  adviceModalContent: {
    backgroundColor: 'white',
    borderRadius: 12,
    padding: 20,
    width: '90%',
    maxWidth: 400,
    maxHeight: '80%',
    paddingBottom: 0,
  },
  adviceModalHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    borderBottomWidth: 1,
    borderBottomColor: '#e0e0e0',
    paddingBottom: 12,
    marginBottom: 0,
  },
  adviceModalTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#333',
  },
  adviceModalBody: {
    marginTop: 16,
  },
  adviceModalSection: {
    marginBottom: 16,
  },
  adviceModalSectionTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: '#0B36A1',
    marginBottom: 8,
  },
  adviceModalDetails: {
    backgroundColor: '#f8f8f8',
    padding: 12,
    borderRadius: 8,
  },
  adviceModalText: {
    fontSize: 14,
    color: '#333',
    flex: 1,
    lineHeight: 20,
  },
  boldText: {
    fontWeight: '600',
    color: '#0B36A1',
  },
  bulletPointContainer: {
    flexDirection: 'row',
    marginBottom: 8,
    paddingRight: 8,
  },
  bulletPoint: {
    fontSize: 14,
    color: '#333',
    marginRight: 8,
    width: 8,
  },
});

export default AdviceInfoModal; 