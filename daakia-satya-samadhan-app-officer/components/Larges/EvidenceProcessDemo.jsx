import React, { useState, useEffect, useRef } from 'react';
import {
  View,
  Text,
  TouchableOpacity,
  Modal,
  StyleSheet,
  Animated,
  Dimensions,
  ScrollView,
  Easing,
} from 'react-native';
import MaterialCommunityIcons from '@expo/vector-icons/MaterialCommunityIcons';
import { LinearGradient } from 'expo-linear-gradient';
import LottieView from 'lottie-react-native';
import { Colors } from '../../constants/colors';

const { width, height } = Dimensions.get('window');

const EvidenceProcessDemo = ({ isVisible, onClose }) => {
  const [currentStep, setCurrentStep] = useState(0);
  const [autoProgress, setAutoProgress] = useState(true);
  const [progressTimer, setProgressTimer] = useState(0);
  
  // Animation values
  const fadeAnim = useRef(new Animated.Value(0)).current;
  const slideAnim = useRef(new Animated.Value(50)).current;
  const scaleAnim = useRef(new Animated.Value(0.8)).current;
  const pulseAnim = useRef(new Animated.Value(1)).current;
  const progressAnim = useRef(new Animated.Value(0)).current;
  const stepTransitionAnim = useRef(new Animated.Value(0)).current;
  const bounceAnim = useRef(new Animated.Value(0)).current;
  const rotateAnim = useRef(new Animated.Value(0)).current;
  
  // New animation values for enhanced effects
  const cardSlideAnim = useRef(new Animated.Value(0)).current;
  const buttonScaleAnim = useRef(new Animated.Value(1)).current;
  const selectionAnim = useRef(new Animated.Value(0)).current;
  const labModalAnim = useRef(new Animated.Value(0)).current;
  const labOptionAnim = useRef(new Animated.Value(0)).current;
  const deptOptionAnim = useRef(new Animated.Value(0)).current;
  const confirmAnim = useRef(new Animated.Value(0)).current;
  const successAnim = useRef(new Animated.Value(0)).current;
  const qrAnim = useRef(new Animated.Value(0)).current;
  const submitAnim = useRef(new Animated.Value(0)).current;

  const demoSteps = [
    {
      id: 1,
      title: "Step 1: Share Evidence to Lab",
      description: "Press the share button (🔗) on any evidence card to assign it to a lab and department.",
      icon: "share-circle",
      color: "#4CAF50",
      action: "Press Share Button",
      target: "Evidence Card"
    },
    {
      id: 2,
      title: "Step 2: Select Lab & Departments",
      description: "Choose the appropriate lab and departments from the modal that opens.",
      icon: "domain",
      color: "#2196F3",
      action: "Select Lab & Dept",
      target: "Lab Modal"
    },
    {
      id: 3,
      title: "Step 3: Enter Selection Mode",
      description: "Long press on any evidence card that has lab information to enter selection mode.",
      icon: "gesture-tap-hold",
      color: "#FF9800",
      action: "Long Press",
      target: "Evidence Card"
    },
    {
      id: 4,
      title: "Step 4: Select Multiple Evidences",
      description: "Tap on evidence cards to select multiple items. Only evidences with lab info can be selected.",
      icon: "checkbox-multiple-marked-circle",
      color: "#9C27B0",
      action: "Multi-Select",
      target: "Evidence Cards"
    },
    {
      id: 5,
      title: "Step 5: Submit to Forensic",
      description: "Press 'SUBMIT TO FORENSIC' button to send selected evidences. A QR code will be generated.",
      icon: "qrcode-scan",
      color: "#E91E63",
      action: "Submit",
      target: "Forensic Button"
    },
    {
      id: 6,
      title: "Alternative: Submit to Court",
      description: "You can also submit the case directly to judiciary using 'SUBMIT TO JUDICIARY' button.",
      icon: "gavel",
      color: "#795548",
      action: "Submit to Court",
      target: "Judiciary Button"
    }
  ];

  useEffect(() => {
    if (isVisible) {
      setCurrentStep(0);
      setProgressTimer(0);
      startAnimations();
      // Reset lab modal animations
      labModalAnim.setValue(0);
      labOptionAnim.setValue(0);
      deptOptionAnim.setValue(0);
      confirmAnim.setValue(0);
      successAnim.setValue(0);
    } else {
      resetAnimations();
    }
  }, [isVisible]);

  // Auto progress effect
  useEffect(() => {
    if (isVisible && autoProgress) {
      setProgressTimer(0);
      
      const timer = setTimeout(() => {
        if (currentStep < demoSteps.length - 1) {
          setCurrentStep(currentStep + 1);
        } else {
          onClose();
        }
      }, 5000); // Auto progress every 5 seconds

      return () => clearTimeout(timer);
    }
  }, [currentStep, isVisible, autoProgress]);

  // Step-specific animations
  useEffect(() => {
    if (isVisible) {
      // Step 0: Animate share button touch and show lab selection
      if (currentStep === 0) {
        setTimeout(() => {
          animateShareButton();
        }, 1000);
      }
      
      // Step 1: Show lab selection animation
      if (currentStep === 1) {
        setTimeout(() => {
          animateLabModal();
        }, 500);
      }
      
      // Step 2: Animate long press
      if (currentStep === 2) {
        // Hide lab modal first
        hideLabModal();
        setTimeout(() => {
          animateLongPress();
        }, 1000);
      }
      
      // Step 3: Animate selection
      if (currentStep === 3) {
        setTimeout(() => {
          animateSelection();
        }, 1000);
      }
      
      // Step 4: Animate submit button
      if (currentStep === 4) {
        setTimeout(() => {
          animateSubmit();
        }, 1000);
      }
      
      // Step 5: Animate judiciary button
      if (currentStep === 5) {
        setTimeout(() => {
          animateJudiciary();
        }, 1000);
      }
    }
  }, [currentStep, isVisible]);

  // Progress timer effect
  useEffect(() => {
    if (isVisible && autoProgress) {
      const interval = setInterval(() => {
        setProgressTimer(prev => {
          if (prev >= 100) {
            return 0;
          }
          return prev + 2; // Increment by 2% every 100ms (5 seconds total)
        });
      }, 100);

      return () => clearInterval(interval);
    }
  }, [isVisible, autoProgress, currentStep]);

  const startAnimations = () => {
    Animated.parallel([
      Animated.timing(fadeAnim, {
        toValue: 1,
        duration: 400,
        easing: Easing.out(Easing.cubic),
        useNativeDriver: true,
      }),
      Animated.timing(slideAnim, {
        toValue: 0,
        duration: 500,
        easing: Easing.out(Easing.back(1.2)),
        useNativeDriver: true,
      }),
      Animated.spring(scaleAnim, {
        toValue: 1,
        tension: 100,
        friction: 8,
        useNativeDriver: true,
      }),
    ]).start();

    startPulseAnimation();
    startProgressAnimation();
    startRotateAnimation();
  };

  const startPulseAnimation = () => {
    Animated.loop(
      Animated.sequence([
        Animated.timing(pulseAnim, {
          toValue: 1.15,
          duration: 1000,
          easing: Easing.inOut(Easing.sin),
          useNativeDriver: true,
        }),
        Animated.timing(pulseAnim, {
          toValue: 1,
          duration: 1000,
          easing: Easing.inOut(Easing.sin),
          useNativeDriver: true,
        }),
      ])
    ).start();
  };

  const startProgressAnimation = () => {
    Animated.timing(progressAnim, {
      toValue: currentStep / (demoSteps.length - 1),
      duration: 800,
      easing: Easing.out(Easing.cubic),
      useNativeDriver: false,
    }).start();
  };

  const startRotateAnimation = () => {
    Animated.loop(
      Animated.timing(rotateAnim, {
        toValue: 1,
        duration: 3000,
        easing: Easing.linear,
        useNativeDriver: true,
      })
    ).start();
  };

  const animateShareButton = () => {
    Animated.sequence([
      Animated.timing(buttonScaleAnim, {
        toValue: 0.8,
        duration: 150,
        easing: Easing.out(Easing.cubic),
        useNativeDriver: true,
      }),
      Animated.timing(buttonScaleAnim, {
        toValue: 1.2,
        duration: 150,
        easing: Easing.out(Easing.cubic),
        useNativeDriver: true,
      }),
      Animated.timing(buttonScaleAnim, {
        toValue: 1,
        duration: 100,
        easing: Easing.out(Easing.cubic),
        useNativeDriver: true,
      }),
    ]).start();
  };

  const animateLabSelection = () => {
    // This function is called but we'll handle it in animateLabModal
    animateLabModal();
  };

  const animateLabModal = () => {
    Animated.timing(labModalAnim, {
      toValue: 1,
      duration: 400,
      easing: Easing.out(Easing.back(1.2)),
      useNativeDriver: true,
    }).start(() => {
      // Animate lab options
      setTimeout(() => {
        animateLabOptions();
      }, 200);
    });
  };

  const animateLabOptions = () => {
    Animated.sequence([
      Animated.timing(labOptionAnim, {
        toValue: 1,
        duration: 300,
        easing: Easing.out(Easing.cubic),
        useNativeDriver: true,
      }),
      Animated.timing(deptOptionAnim, {
        toValue: 1,
        duration: 300,
        easing: Easing.out(Easing.cubic),
        useNativeDriver: true,
      }),
    ]).start(() => {
      // Show confirm animation
      setTimeout(() => {
        animateConfirm();
      }, 500);
    });
  };

  const animateConfirm = () => {
    Animated.timing(confirmAnim, {
      toValue: 1,
      duration: 300,
      easing: Easing.out(Easing.cubic),
      useNativeDriver: true,
    }).start(() => {
      // Show success animation
      setTimeout(() => {
        animateSuccess();
      }, 500);
    });
  };

  const animateSuccess = () => {
    Animated.timing(successAnim, {
      toValue: 1,
      duration: 400,
      easing: Easing.out(Easing.cubic),
      useNativeDriver: true,
    }).start(() => {
      // Hide lab modal
      setTimeout(() => {
        hideLabModal();
      }, 1000);
    });
  };

  const hideLabModal = () => {
    Animated.timing(labModalAnim, {
      toValue: 0,
      duration: 300,
      easing: Easing.in(Easing.cubic),
      useNativeDriver: true,
    }).start();
  };

  const animateLongPress = () => {
    Animated.sequence([
      Animated.timing(cardSlideAnim, {
        toValue: 1,
        duration: 200,
        easing: Easing.out(Easing.cubic),
        useNativeDriver: true,
      }),
      Animated.timing(cardSlideAnim, {
        toValue: 0,
        duration: 200,
        easing: Easing.in(Easing.cubic),
        useNativeDriver: true,
      }),
    ]).start();
  };

  const animateSelection = () => {
    Animated.timing(selectionAnim, {
      toValue: 1,
      duration: 400,
      easing: Easing.out(Easing.back(1.2)),
      useNativeDriver: true,
    }).start();
  };

  const animateSubmit = () => {
    Animated.sequence([
      Animated.timing(submitAnim, {
        toValue: 1,
        duration: 300,
        easing: Easing.out(Easing.cubic),
        useNativeDriver: true,
      }),
      Animated.timing(qrAnim, {
        toValue: 1,
        duration: 500,
        easing: Easing.out(Easing.back(1.2)),
        useNativeDriver: true,
      }),
    ]).start();
  };

  const animateJudiciary = () => {
    Animated.timing(submitAnim, {
      toValue: 1,
      duration: 400,
      easing: Easing.out(Easing.cubic),
      useNativeDriver: true,
    }).start();
  };

  const animateStepTransition = () => {
    Animated.sequence([
      Animated.timing(stepTransitionAnim, {
        toValue: 1,
        duration: 200,
        easing: Easing.out(Easing.cubic),
        useNativeDriver: true,
      }),
      Animated.spring(bounceAnim, {
        toValue: 1,
        tension: 150,
        friction: 8,
        useNativeDriver: true,
      }),
      Animated.timing(stepTransitionAnim, {
        toValue: 0,
        duration: 200,
        easing: Easing.in(Easing.cubic),
        useNativeDriver: true,
      }),
    ]).start(() => {
      bounceAnim.setValue(0);
    });
  };

  const resetAnimations = () => {
    fadeAnim.setValue(0);
    slideAnim.setValue(50);
    scaleAnim.setValue(0.8);
    pulseAnim.setValue(1);
    progressAnim.setValue(0);
    stepTransitionAnim.setValue(0);
    bounceAnim.setValue(0);
    rotateAnim.setValue(0);
    cardSlideAnim.setValue(0);
    buttonScaleAnim.setValue(1);
    selectionAnim.setValue(0);
    labModalAnim.setValue(0);
    labOptionAnim.setValue(0);
    deptOptionAnim.setValue(0);
    confirmAnim.setValue(0);
    successAnim.setValue(0);
    qrAnim.setValue(0);
    submitAnim.setValue(0);
  };

  const nextStep = () => {
    if (currentStep < demoSteps.length - 1) {
      animateStepTransition();
      setCurrentStep(currentStep + 1);
    } else {
      onClose();
    }
  };

  const prevStep = () => {
    if (currentStep > 0) {
      animateStepTransition();
      setCurrentStep(currentStep - 1);
    }
  };

  const toggleAutoProgress = () => {
    setAutoProgress(!autoProgress);
  };

  const skipDemo = () => {
    onClose();
  };

  const currentStepData = demoSteps[currentStep];

  const renderEvidenceCard = () => {
    const cardScale = stepTransitionAnim.interpolate({
      inputRange: [0, 1],
      outputRange: [1, 0.95],
    });

    const cardSlide = cardSlideAnim.interpolate({
      inputRange: [0, 1],
      outputRange: [0, -10],
    });

    const selectionScale = selectionAnim.interpolate({
      inputRange: [0, 1],
      outputRange: [0, 1],
    });

    const isSelected = currentStep === 2 || currentStep === 3;
    const isHighlighted = currentStep === 0 || currentStep === 2 || currentStep === 3;

    return (
      <Animated.View
        style={[
          styles.evidenceCard,
          {
            transform: [
              { scale: Animated.multiply(cardScale, buttonScaleAnim) },
              { translateX: cardSlide }
            ],
            opacity: isHighlighted ? 1 : 0.7,
            borderColor: isSelected ? Colors.primary : Colors.border,
            borderWidth: isSelected ? 2 : 1,
            backgroundColor: isSelected ? 'rgba(11, 54, 161, 0.05)' : Colors.background,
          }
        ]}
      >
        {isSelected && (
          <Animated.View 
            style={[
              styles.selectedIndicator,
              {
                transform: [{ scale: selectionScale }],
                opacity: selectionScale,
              }
            ]}
          >
            <MaterialCommunityIcons name="check-circle" size={24} color={Colors.primary} />
          </Animated.View>
        )}
        
        <View style={styles.imageContainer}>
          <View style={styles.mediaContainer}>
            <MaterialCommunityIcons
              name="image"
              size={40}
              color={Colors.lightText}
            />
          </View>
        </View>
        
        <View style={styles.evidenceDetails}>
          <Text style={styles.evidenceTitle}>Sample Evidence</Text>
          
          <View style={styles.detailRow}>
            <Text style={styles.evidenceLabel}>Type:</Text>
            <Text style={styles.evidenceType}>Digital</Text>
          </View>
          
          <View style={styles.detailRow}>
            <Text style={styles.evidenceLabel}>Lab:</Text>
            <Text style={styles.evidenceType}>Forensic Lab Delhi</Text>
          </View>
          
          <View style={styles.detailRow}>
            <Text style={styles.evidenceLabel}>Department:</Text>
            <Text style={styles.evidenceType}>DNA Analysis</Text>
          </View>
          
          <View style={styles.detailRow}>
            <Text style={styles.evidenceLabel}>Status:</Text>
            <View style={styles.statusBadge}>
              <Text style={styles.statusText}>Ready</Text>
            </View>
          </View>
        </View>
        
        <Animated.View
          style={[
            styles.shareButton,
            {
              transform: [{
                scale: currentStep === 0 ? pulseAnim : 1
              }]
            }
          ]}
        >
          <MaterialCommunityIcons
            name="share-circle"
            size={24}
            color={currentStep === 0 ? "#4CAF50" : Colors.primary}
          />
        </Animated.View>
      </Animated.View>
    );
  };

  const renderLabSelectionModal = () => {
    const modalScale = labModalAnim.interpolate({
      inputRange: [0, 1],
      outputRange: [0.8, 1],
    });

    const labOptionSlide = labOptionAnim.interpolate({
      inputRange: [0, 1],
      outputRange: [50, 0],
    });

    const deptOptionSlide = deptOptionAnim.interpolate({
      inputRange: [0, 1],
      outputRange: [50, 0],
    });

    const confirmScale = confirmAnim.interpolate({
      inputRange: [0, 1],
      outputRange: [0, 1],
    });

    const successScale = successAnim.interpolate({
      inputRange: [0, 1],
      outputRange: [0, 1],
    });

    return (
      <Animated.View
        style={[
          styles.labModalOverlay,
          {
            opacity: labModalAnim,
          },
        ]}
      >
        <Animated.View
          style={[
            styles.labModalContent,
            {
              transform: [{ scale: modalScale }],
            },
          ]}
        >
          <View style={styles.modalHeader}>
            <Text style={styles.modalTitle}>Select Lab & Department</Text>
          </View>
          
          <View style={styles.modalBody}>
            <Animated.View 
              style={[
                styles.modalOption,
                {
                  transform: [{ translateX: labOptionSlide }],
                  opacity: labOptionAnim,
                }
              ]}
            >
              <MaterialCommunityIcons name="hospital-building" size={20} color={Colors.primary} />
              <Text style={styles.modalOptionText}>🏥 Forensic Lab Delhi</Text>
            </Animated.View>
            
            <Animated.View 
              style={[
                styles.modalOption,
                {
                  transform: [{ translateX: deptOptionSlide }],
                  opacity: deptOptionAnim,
                }
              ]}
            >
              <MaterialCommunityIcons name="test-tube" size={20} color="#9C27B0" />
              <Text style={styles.modalOptionText}>📋 DNA Analysis</Text>
            </Animated.View>
          </View>
          
          <Animated.View 
            style={[
              styles.modalFooter,
              {
                transform: [{ scale: confirmScale }],
                opacity: confirmAnim,
              }
            ]}
          >
            <View style={styles.modalButton}>
              <Text style={styles.modalButtonText}>Confirm Selection</Text>
            </View>
          </Animated.View>

          {/* Success Animation */}
          <Animated.View 
            style={[
              styles.successOverlay,
              {
                opacity: successAnim,
                transform: [{ scale: successScale }],
              }
            ]}
          >
            <LottieView
              source={require('../../assets/animations/success.json')}
              autoPlay
              loop={false}
              style={styles.successAnimation}
            />
            <Text style={styles.successText}>Lab Assigned Successfully!</Text>
          </Animated.View>
        </Animated.View>
      </Animated.View>
    );
  };

  const renderSubmitButtons = () => {
    const submitScale = submitAnim.interpolate({
      inputRange: [0, 1],
      outputRange: [0, 1],
    });

    const qrScale = qrAnim.interpolate({
      inputRange: [0, 1],
      outputRange: [0, 1],
    });

    return (
      <View style={styles.buttonContainer}>
        <Animated.View
          style={[
            styles.submitButton,
            {
              transform: [{ scale: Animated.multiply(submitScale, currentStep === 4 ? pulseAnim : 1) }],
            }
          ]}
        >
          <LinearGradient
            colors={['#0B36A1', '#0B36A1']}
            style={styles.buttonGradient}
          >
            <MaterialCommunityIcons
              name="flask"
              size={16}
              color="white"
              style={{ marginRight: 4 }}
            />
            <Text style={styles.buttonText}>SUBMIT TO FORENSIC</Text>
          </LinearGradient>
        </Animated.View>

        <Animated.View
          style={[
            styles.submitButton,
            {
              transform: [{ scale: Animated.multiply(submitScale, currentStep === 5 ? pulseAnim : 1) }],
            }
          ]}
        >
          <LinearGradient
            colors={['#0B36A1', '#0B36A1']}
            style={styles.buttonGradient}
          >
            <MaterialCommunityIcons
              name="gavel"
              size={16}
              color="white"
              style={{ marginRight: 4 }}
            />
            <Text style={styles.buttonText}>SUBMIT TO JUDICIARY</Text>
          </LinearGradient>
        </Animated.View>

        {/* QR Code Animation */}
        {currentStep === 4 && (
          <Animated.View 
            style={[
              styles.qrOverlay,
              {
                opacity: qrAnim,
                transform: [{ scale: qrScale }],
              }
            ]}
          >
            <View style={styles.qrContainer}>
              <MaterialCommunityIcons name="qrcode" size={60} color={Colors.primary} />
              <Text style={styles.qrText}>QR Code Generated</Text>
              <Text style={styles.qrSubtext}>Evidence sent to Forensic Lab</Text>
            </View>
          </Animated.View>
        )}
      </View>
    );
  };

  const renderStepIndicator = () => {
    const progressWidth = progressAnim.interpolate({
      inputRange: [0, 1],
      outputRange: ['0%', '100%'],
    });

    const rotateInterpolate = rotateAnim.interpolate({
      inputRange: [0, 1],
      outputRange: ['0deg', '360deg'],
    });

    return (
      <View style={styles.stepIndicatorContainer}>
        <View style={styles.progressBarBackground}>
          <Animated.View
            style={[
              styles.progressBarFill,
              { width: progressWidth }
            ]}
          />
        </View>

        <View style={styles.stepDotsContainer}>
          {demoSteps.map((step, index) => (
            <View key={index} style={styles.stepDotWrapper}>
              <View
                style={[
                  styles.stepDot,
                  { backgroundColor: index <= currentStep ? step.color : '#E0E0E0' }
                ]}
              >
                {index < currentStep ? (
                  <MaterialCommunityIcons
                    name="check"
                    size={10}
                    color="white"
                  />
                ) : index === currentStep ? (
                  <MaterialCommunityIcons
                    name={step.icon}
                    size={10}
                    color="white"
                  />
                ) : (
                  <Text style={styles.stepNumber}>{index + 1}</Text>
                )}
              </View>
              <Text 
                style={[
                  styles.stepLabel,
                  {
                    color: index <= currentStep ? step.color : '#999',
                    fontWeight: index === currentStep ? 'bold' : 'normal'
                  }
                ]}
                numberOfLines={1}
                ellipsizeMode="tail"
              >
                {step.action}
              </Text>
            </View>
          ))}
        </View>
      </View>
    );
  };

  return (
    <Modal visible={isVisible} animationType="fade" transparent={true}>
      <View style={styles.overlay}>
        <Animated.View
          style={[
            styles.demoContainer,
            {
              opacity: fadeAnim,
              transform: [
                { translateY: slideAnim },
                { scale: scaleAnim }
              ]
            }
          ]}
        >
          <ScrollView showsVerticalScrollIndicator={false}>
            {/* Header */}
            <View style={styles.header}>
              <View style={styles.headerTitleContainer}>
                <MaterialCommunityIcons 
                  name="school" 
                  size={24} 
                  color={Colors.primary} 
                />
                <Text style={styles.headerTitle}>Evidence Process Guide</Text>
              </View>
              <TouchableOpacity onPress={onClose} style={styles.closeButton}>
                <MaterialCommunityIcons name="close" size={24} color={Colors.lightText} />
              </TouchableOpacity>
            </View>

            {/* Step Indicator */}
            {renderStepIndicator()}

            {/* Current Step */}
            <View style={styles.stepContainer}>
              <View style={[styles.stepIconContainer, { backgroundColor: currentStepData.color }]}>
                <MaterialCommunityIcons 
                  name={currentStepData.icon} 
                  size={32} 
                  color="white" 
                />
              </View>
              
              <Text style={styles.stepTitle}>{currentStepData.title}</Text>
              <Text style={styles.stepDescription}>{currentStepData.description}</Text>
              
              <View style={styles.actionContainer}>
                <View style={styles.actionBadge}>
                  <Text style={styles.actionText}>Action: {currentStepData.action}</Text>
                </View>
                <View style={styles.targetBadge}>
                  <Text style={styles.targetText}>Target: {currentStepData.target}</Text>
                </View>
              </View>
            </View>

            {/* Interactive Demo */}
            <View style={styles.demoContainer}>
              <Text style={styles.demoTitle}>Live Demo:</Text>
              
              {/* Show different demos based on current step */}
              {(currentStep === 0 || currentStep === 2 || currentStep === 3) && renderEvidenceCard()}
              
              {currentStep === 1 && renderLabSelectionModal()}
              
              {(currentStep === 4 || currentStep === 5) && renderSubmitButtons()}
            </View>
          </ScrollView>

          {/* Navigation */}
          <View style={styles.navigationContainer}>
            <TouchableOpacity
              onPress={prevStep}
              style={[styles.navButton, currentStep === 0 && styles.disabledButton]}
              disabled={currentStep === 0}
            >
              <MaterialCommunityIcons name="arrow-left" size={20} color="white" />
              <Text style={styles.navButtonText}>Previous</Text>
            </TouchableOpacity>

            <Text style={styles.stepCounter}>
              {currentStep + 1} of {demoSteps.length}
            </Text>

            <TouchableOpacity
              onPress={nextStep}
              style={[styles.navButton, styles.nextButton]}
            >
              <Text style={styles.navButtonText}>
                {currentStep === demoSteps.length - 1 ? 'Finish' : 'Next'}
              </Text>
              <MaterialCommunityIcons 
                name={currentStep === demoSteps.length - 1 ? "check" : "arrow-right"} 
                size={20} 
                color="white" 
              />
            </TouchableOpacity>
          </View>
        </Animated.View>
      </View>
    </Modal>
  );
};

const styles = StyleSheet.create({
  overlay: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.8)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  demoContainer: {
    width: 300,
    maxHeight: height * 0.75,
    backgroundColor: Colors.background,
    borderRadius: 16,
    padding: 0,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: 12,
    borderBottomWidth: 1,
    borderBottomColor: Colors.border,
  },
  headerTitleContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  headerTitle: {
    fontSize: 16,
    fontWeight: 'bold',
    color: Colors.black,
    marginLeft: 6,
    fontFamily: 'Roboto_Bold',
  },
  closeButton: {
    padding: 8,
    borderRadius: 20,
    backgroundColor: 'rgba(0, 0, 0, 0.05)',
  },
  stepIndicatorContainer: {
    paddingVertical: 12,
    paddingHorizontal: 12,
    backgroundColor: '#FAFAFA',
    borderBottomWidth: 1,
    borderBottomColor: Colors.border,
  },
  progressBarBackground: {
    height: 4,
    backgroundColor: '#E0E0E0',
    borderRadius: 2,
    marginBottom: 16,
    overflow: 'hidden',
  },
  progressBarFill: {
    height: '100%',
    backgroundColor: Colors.primary,
    borderRadius: 2,
  },
  stepDotsContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-start',
    paddingHorizontal: 8,
  },
  stepDotWrapper: {
    alignItems: 'center',
    flex: 1,
    minWidth: 0,
    height: 40,
    justifyContent: 'space-between',
  },
  stepDot: {
    width: 20,
    height: 20,
    borderRadius: 10,
    backgroundColor: '#E0E0E0',
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: 4,
  },
  stepNumber: {
    fontSize: 8,
    fontWeight: 'bold',
    color: '#666',
    fontFamily: 'Roboto',
  },
  stepLabel: {
    fontSize: 8,
    textAlign: 'center',
    fontFamily: 'Roboto',
    numberOfLines: 1,
    ellipsizeMode: 'tail',
    maxWidth: '100%',
    lineHeight: 10,
  },
  stepContainer: {
    padding: 12,
    alignItems: 'center',
  },
  stepIconContainer: {
    width: 48,
    height: 48,
    borderRadius: 24,
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: 12,
  },
  stepTitle: {
    fontSize: 16,
    fontWeight: 'bold',
    color: Colors.black,
    textAlign: 'center',
    marginBottom: 6,
    fontFamily: 'Roboto_Bold',
  },
  stepDescription: {
    fontSize: 12,
    color: Colors.lightText,
    textAlign: 'center',
    lineHeight: 16,
    marginBottom: 12,
    fontFamily: 'Roboto',
  },
  actionContainer: {
    flexDirection: 'row',
    gap: 12,
  },
  actionBadge: {
    backgroundColor: '#E3F2FD',
    paddingVertical: 6,
    paddingHorizontal: 12,
    borderRadius: 16,
  },
  actionText: {
    fontSize: 12,
    color: '#1976D2',
    fontWeight: '500',
    fontFamily: 'Roboto',
  },
  targetBadge: {
    backgroundColor: '#F3E5F5',
    paddingVertical: 6,
    paddingHorizontal: 12,
    borderRadius: 16,
  },
  targetText: {
    fontSize: 12,
    color: '#7B1FA2',
    fontWeight: '500',
    fontFamily: 'Roboto',
  },
  demoContainer: {
    margin: 12,
    padding: 8,
    backgroundColor: '#F8F9FA',
    borderRadius: 12,
    borderWidth: 1,
    borderColor: Colors.border,
  },
  demoTitle: {
    fontSize: 12,
    fontWeight: 'bold',
    color: Colors.black,
    marginBottom: 8,
    fontFamily: 'Roboto_Bold',
  },
  evidenceCard: {
    borderColor: Colors.border,
    borderWidth: 1,
    marginHorizontal: '2%',
    marginBottom: 16,
    padding: 8,
    borderRadius: 8,
    flexDirection: 'row',
    position: 'relative',
  },
  selectedIndicator: {
    position: 'absolute',
    top: 8,
    right: 8,
    zIndex: 1,
  },
  imageContainer: {
    width: 60,
    marginRight: 8,
    justifyContent: 'center',
    alignItems: 'center',
  },
  mediaContainer: {
    width: 60,
    height: 60,
    borderRadius: 6,
    backgroundColor: '#f0f0f0',
    justifyContent: 'center',
    alignItems: 'center',
  },
  evidenceDetails: {
    flex: 1,
    gap: 6,
  },
  detailRow: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 4,
  },
  evidenceLabel: {
    fontSize: 10,
    color: Colors.lightText,
    width: 60,
  },
  evidenceType: {
    fontSize: 10,
    color: Colors.lightText,
    flex: 1,
  },
  evidenceTitle: {
    fontSize: 13,
    color: Colors.black,
    marginBottom: 6,
  },
  shareButton: {
    padding: 4,
    justifyContent: 'center',
    alignItems: 'center',
  },
  statusBadge: {
    backgroundColor: '#4CAF50',
    paddingHorizontal: 8,
    paddingVertical: 2,
    borderRadius: 12,
  },
  statusText: {
    color: 'white',
    fontSize: 10,
    fontWeight: 'bold',
    fontFamily: 'Roboto',
  },
  labModalOverlay: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    justifyContent: 'center',
    alignItems: 'center',
    zIndex: 1000,
  },
  labModalContent: {
    backgroundColor: Colors.background,
    borderRadius: 16,
    padding: 0,
    width: 280,
    maxHeight: height * 0.5,
    elevation: 10,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.3,
    shadowRadius: 8,
  },
  modalHeader: {
    padding: 20,
    borderBottomWidth: 1,
    borderBottomColor: Colors.border,
  },
  modalTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: Colors.black,
    textAlign: 'center',
    fontFamily: 'Roboto_Bold',
  },
  modalBody: {
    padding: 20,
  },
  modalOption: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#F8F9FA',
    padding: 16,
    borderRadius: 8,
    marginBottom: 12,
    borderWidth: 1,
    borderColor: Colors.border,
  },
  modalOptionText: {
    fontSize: 16,
    color: Colors.black,
    marginLeft: 12,
    fontFamily: 'Roboto',
  },
  modalFooter: {
    padding: 20,
    borderTopWidth: 1,
    borderTopColor: Colors.border,
  },
  modalButton: {
    backgroundColor: '#0B36A1',
    paddingVertical: 16,
    paddingHorizontal: 24,
    borderRadius: 50,
    alignItems: 'center',
    justifyContent: 'center',
  },
  modalButtonText: {
    color: '#FFFFFF',
    fontSize: 16,
    fontWeight: '500',
    fontFamily: 'Roboto',
  },
  successOverlay: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    backgroundColor: 'rgba(76, 175, 80, 0.9)',
    justifyContent: 'center',
    alignItems: 'center',
    borderRadius: 16,
  },
  successAnimation: {
    width: 100,
    height: 100,
  },
  successText: {
    color: 'white',
    fontSize: 16,
    fontWeight: 'bold',
    marginTop: 16,
    textAlign: 'center',
    fontFamily: 'Roboto_Bold',
  },
  buttonContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginHorizontal: '2%',
    marginTop: 12,
    marginBottom: 16,
    position: 'relative',
  },
  submitButton: {
    flex: 1,
    backgroundColor: '#0B36A1',
    padding: 8,
    borderRadius: 50,
    alignItems: 'center',
    justifyContent: 'center',
    marginHorizontal: 4,
  },
  buttonGradient: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 8,
    paddingHorizontal: 16,
    borderRadius: 50,
  },
  buttonText: {
    color: '#FFFFFF',
    fontSize: 12,
    fontWeight: '500',
    fontFamily: 'Roboto',
  },
  qrOverlay: {
    position: 'absolute',
    top: -100,
    left: 0,
    right: 0,
    alignItems: 'center',
    zIndex: 1000,
  },
  qrContainer: {
    backgroundColor: Colors.background,
    padding: 20,
    borderRadius: 12,
    alignItems: 'center',
    elevation: 8,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.3,
    shadowRadius: 8,
  },
  qrText: {
    fontSize: 16,
    fontWeight: 'bold',
    color: Colors.black,
    marginTop: 12,
    fontFamily: 'Roboto_Bold',
  },
  qrSubtext: {
    fontSize: 12,
    color: Colors.lightText,
    marginTop: 4,
    fontFamily: 'Roboto',
  },
  navigationContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: 12,
    borderTopWidth: 1,
    borderTopColor: Colors.border,
  },
  navButton: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: Colors.lightText,
    paddingVertical: 12,
    paddingHorizontal: 16,
    borderRadius: 50,
  },
  nextButton: {
    backgroundColor: '#0B36A1',
  },
  disabledButton: {
    backgroundColor: Colors.disabled,
    opacity: 0.5,
  },
  navButtonText: {
    color: '#FFFFFF',
    fontSize: 14,
    fontWeight: '500',
    marginHorizontal: 4,
    fontFamily: 'Roboto',
  },
  stepCounter: {
    fontSize: 14,
    color: Colors.lightText,
    fontFamily: 'Roboto',
  },
});

export default EvidenceProcessDemo;