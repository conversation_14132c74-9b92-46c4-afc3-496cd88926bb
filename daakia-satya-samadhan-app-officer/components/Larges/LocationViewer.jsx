import React, { useState, useRef, useEffect, useMemo } from 'react';
import { 
  View, 
  Text, 
  StyleSheet, 
  TouchableOpacity, 
  Platform,
  Dimensions,
  Animated,
  Modal
} from 'react-native';
import MapView, { <PERSON>er, PROVIDER_GOOGLE } from 'react-native-maps';
import { MaterialIcons, Ionicons } from '@expo/vector-icons';
import { Colors } from '../../constants/colors';

const { width, height } = Dimensions.get('window');

const LocationViewer = ({ isVisible, onClose, latitude, longitude, title = "Location" }) => {
  const [mapType, setMapType] = useState('standard');
  const [zoomLevel, setZoomLevel] = useState(0.01);
  const mapRef = useRef(null);
  const pulseAnim = useRef(new Animated.Value(0.5)).current;

  // Create region from provided coordinates
  const region = useMemo(() => {
    if (!latitude || !longitude) return null;
    return {
      latitude: parseFloat(latitude),
      longitude: parseFloat(longitude),
      latitudeDelta: zoomLevel,
      longitudeDelta: zoomLevel,
    };
  }, [latitude, longitude, zoomLevel]);

  // Start pulse animation
  useEffect(() => {
    if (isVisible) {
      Animated.loop(
        Animated.sequence([
          Animated.timing(pulseAnim, {
            toValue: 1,
            duration: 1000,
            useNativeDriver: true,
          }),
          Animated.timing(pulseAnim, {
            toValue: 0.5,
            duration: 1000,
            useNativeDriver: true,
          }),
        ])
      ).start();
    }
  }, [pulseAnim, isVisible]);

  // Map controls
  const mapControls = useMemo(() => ({
    toggleMapType: () => setMapType(type => type === 'standard' ? 'satellite' : 'standard'),
    zoomIn: () => {
      const newZoom = Math.max(0.001, zoomLevel / 2);
      setZoomLevel(newZoom);
      if (mapRef.current && region) {
        mapRef.current.animateToRegion({
          ...region,
          latitudeDelta: newZoom,
          longitudeDelta: newZoom,
        }, 300);
      }
    },
    zoomOut: () => {
      const newZoom = Math.min(0.1, zoomLevel * 2);
      setZoomLevel(newZoom);
      if (mapRef.current && region) {
        mapRef.current.animateToRegion({
          ...region,
          latitudeDelta: newZoom,
          longitudeDelta: newZoom,
        }, 300);
      }
    },
    recenterMap: () => {
      if (mapRef.current && region) {
        mapRef.current.animateToRegion(region, 300);
      }
    }
  }), [zoomLevel, region]);

  if (!isVisible) return null;

  // Check if we have valid coordinates
  const hasValidCoordinates = latitude && longitude && 
    !isNaN(parseFloat(latitude)) && !isNaN(parseFloat(longitude));

  return (
    <Modal
      visible={isVisible}
      animationType="slide"
      presentationStyle="pageSheet"
      onRequestClose={onClose}
    >
      <View style={styles.container}>
        {/* Header */}
        <View style={styles.header}>
          <Text style={styles.headerTitle}>{title}</Text>
          <TouchableOpacity
            style={styles.closeButton}
            onPress={onClose}
          >
            <Ionicons name="close" size={24} color={Colors.black} />
          </TouchableOpacity>
        </View>

        {hasValidCoordinates ? (
          <>
            {/* Map Container */}
            <View style={styles.mapContainer}>
              <MapView
                ref={mapRef}
                style={styles.map}
                provider={Platform.OS === 'ios' ? undefined : PROVIDER_GOOGLE}
                initialRegion={region}
                mapType={mapType}
                showsUserLocation={false}
                showsMyLocationButton={false}
                showsCompass={true}
                showsScale={true}
                rotateEnabled={true}
                scrollEnabled={true}
                zoomEnabled={true}
                pitchEnabled={true}
                onMapReady={mapControls.recenterMap}
              >
                <Marker 
                  coordinate={{
                    latitude: parseFloat(latitude),
                    longitude: parseFloat(longitude)
                  }} 
                  title="Case Location"
                  description={`${latitude}, ${longitude}`}
                >
                  <View style={styles.markerContainer}>
                    <Animated.View 
                      style={[
                        styles.markerPulse,
                        {
                          transform: [{ scale: pulseAnim }],
                          opacity: pulseAnim.interpolate({
                            inputRange: [0.5, 1],
                            outputRange: [0.3, 0]
                          })
                        }
                      ]} 
                    />
                    <MaterialIcons name="location-pin" size={35} color="#DC2626" />
                  </View>
                </Marker>
              </MapView>
              
              {/* Map Controls */}
              <View style={styles.mapControls}>
                <TouchableOpacity style={styles.controlButton} onPress={mapControls.toggleMapType}>
                  <MaterialIcons name={mapType === 'standard' ? 'satellite' : 'map'} size={24} color="#555" />
                </TouchableOpacity>
                <TouchableOpacity style={styles.controlButton} onPress={mapControls.zoomIn}>
                  <MaterialIcons name="add" size={24} color="#555" />
                </TouchableOpacity>
                <TouchableOpacity style={styles.controlButton} onPress={mapControls.zoomOut}>
                  <MaterialIcons name="remove" size={24} color="#555" />
                </TouchableOpacity>
                <TouchableOpacity style={styles.controlButton} onPress={mapControls.recenterMap}>
                  <MaterialIcons name="my-location" size={24} color="#555" />
                </TouchableOpacity>
              </View>
            </View>
            
            {/* Coordinates Info */}
            <View style={styles.infoCard}>
              <Text style={styles.infoTitle}>GPS Coordinates</Text>
              <Text style={styles.coordinateText}>
                Latitude: {parseFloat(latitude).toFixed(6)}
              </Text>
              <Text style={styles.coordinateText}>
                Longitude: {parseFloat(longitude).toFixed(6)}
              </Text>
            </View>
          </>
        ) : (
          <View style={styles.noLocationContainer}>
            <MaterialIcons name="location-off" size={64} color={Colors.lightText} />
            <Text style={styles.noLocationTitle}>No Location Data</Text>
            <Text style={styles.noLocationText}>
              GPS coordinates are not available for this case.
            </Text>
          </View>
        )}

        {/* Close Button */}
        <View style={styles.buttonContainer}>
          <TouchableOpacity
            style={styles.closeActionButton}
            onPress={onClose}
          >
            <Ionicons name="checkmark" size={20} color="white" />
            <Text style={styles.buttonText}>Close</Text>
          </TouchableOpacity>
        </View>
      </View>
    </Modal>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#fff',
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: 20,
    paddingTop: Platform.OS === 'ios' ? 50 : 20,
    paddingBottom: 20,
    borderBottomWidth: 1,
    borderBottomColor: Colors.border,
    backgroundColor: Colors.background,
  },
  headerTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    color: Colors.black,
    fontFamily: 'Roboto_bold',
  },
  closeButton: {
    padding: 8,
  },
  mapContainer: {
    flex: 1,
    margin: 15,
    borderRadius: 15,
    overflow: 'hidden',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.25,
    shadowRadius: 3.84,
    elevation: 5,
  },
  map: {
    flex: 1,
  },
  markerContainer: {
    alignItems: 'center',
  },
  markerPulse: {
    position: 'absolute',
    height: 50,
    width: 50,
    borderRadius: 25,
    backgroundColor: 'rgba(220, 38, 38, 0.2)',
  },
  mapControls: {
    position: 'absolute',
    right: 20,
    bottom: 20,
    backgroundColor: 'white',
    borderRadius: 30,
    padding: 8,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.2,
    shadowRadius: 3,
    elevation: 5,
  },
  controlButton: {
    width: 45,
    height: 45,
    borderRadius: 23,
    backgroundColor: 'white',
    justifyContent: 'center',
    alignItems: 'center',
    marginVertical: 5,
  },
  infoCard: {
    backgroundColor: Colors.background,
    margin: 15,
    padding: 16,
    borderRadius: 12,
    borderWidth: 1,
    borderColor: Colors.border,
  },
  infoTitle: {
    fontSize: 16,
    fontWeight: 'bold',
    color: Colors.black,
    marginBottom: 8,
    fontFamily: 'Roboto_bold',
  },
  coordinateText: {
    fontSize: 14,
    color: Colors.lightText,
    marginVertical: 2,
    fontFamily: 'Roboto',
  },
  noLocationContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 40,
  },
  noLocationTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    color: Colors.black,
    marginTop: 16,
    marginBottom: 8,
    fontFamily: 'Roboto_bold',
  },
  noLocationText: {
    fontSize: 16,
    color: Colors.lightText,
    textAlign: 'center',
    lineHeight: 24,
    fontFamily: 'Roboto',
  },
  buttonContainer: {
    padding: 15,
    backgroundColor: '#fff',
    borderTopWidth: 1,
    borderColor: '#eee',
    paddingBottom: Platform.OS === 'ios' ? 30 : 15,
  },
  closeActionButton: {
    backgroundColor: Colors.primary,
    paddingVertical: 14,
    paddingHorizontal: 20,
    borderRadius: 50,
    alignItems: 'center',
    flexDirection: 'row',
    justifyContent: 'center',
  },
  buttonText: {
    fontFamily: 'Roboto_bold',
    fontSize: 16,
    color: Colors.background,
    marginLeft: 8,
  },
});

export default LocationViewer; 