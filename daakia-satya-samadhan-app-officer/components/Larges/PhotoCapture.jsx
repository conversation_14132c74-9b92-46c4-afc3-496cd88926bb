import React, { useRef, useState, useEffect } from 'react';
import { StyleSheet, Text, TouchableOpacity, View, Image, ScrollView, Modal, Dimensions, ActivityIndicator, Alert } from 'react-native';
import { AntDesign, Ionicons, MaterialIcons } from '@expo/vector-icons';
import { CameraView, useCameraPermissions } from 'expo-camera';
import { BlurView } from 'expo-blur';

const { width, height } = Dimensions.get('window');

const PhotoCapture = ({ setUri, backPressed, batchMode = true }) => {
  const [facing, setFacing] = useState('back');
  const [permission, requestPermission] = useCameraPermissions();
  const cameraRef = useRef();
  const [capturedPhotos, setCapturedPhotos] = useState([]);
  const [showPreviewModal, setShowPreviewModal] = useState(false);
  const [isProcessingPhoto, setIsProcessingPhoto] = useState(false);
  const [isCameraReady, setIsCameraReady] = useState(false);
  const [isCapturing, setIsCapturing] = useState(false);

  // Reset camera ready state when facing changes
  useEffect(() => {
    setIsCameraReady(false);
  }, [facing]);

  const CameraPermissionRequest = () => (
    <View style={styles.permissionContainer}>
      <Ionicons name="camera-outline" size={60} color="#4dabf7" />
      <Text style={styles.permissionText}>We need your permission to use the camera</Text>
      <TouchableOpacity style={styles.permissionButton} onPress={requestPermission}>
        <Text style={styles.permissionButtonText}>Grant Permission</Text>
      </TouchableOpacity>
    </View>
  );

  if (!permission) {
    return (
      <View style={styles.permissionContainer}>
        <Text style={styles.permissionText}>Loading camera...</Text>
      </View>
    );
  }

  if (!permission.granted) {
    return <CameraPermissionRequest />;
  }

  const toggleCameraFacing = () => {
    if (!isProcessingPhoto && !isCapturing) {
      setFacing((current) => (current === 'back' ? 'front' : 'back'));
    }
  };

  const handleCameraReady = () => {
    setIsCameraReady(true);
  };

  const takePicture = async () => {
    // Prevent multiple simultaneous captures
    if (isCapturing || isProcessingPhoto || !isCameraReady || !cameraRef.current) {
      return;
    }

    try {
      setIsCapturing(true);
      setIsProcessingPhoto(true);
      
      // Add a small delay to ensure camera is fully ready
      await new Promise(resolve => setTimeout(resolve, 100));
      
      const options = { 
        quality: 0.8, 
        base64: false,
        skipProcessing: false,
        exif: false
      };
      
      const data = await cameraRef.current.takePictureAsync(options);
      
      // Validate the captured image
      if (!data || !data.uri || data.width === 0 || data.height === 0) {
        throw new Error('Invalid image data captured');
      }

      if (batchMode) {
        // In batch mode, add to captured photos array
        setCapturedPhotos(prev => [...prev, data.uri]);
      } else {
        // In single mode, return immediately
        setUri(data.uri);
      }
    } catch (error) {
      console.error('Error taking picture:', error);
      Alert.alert(
        'Camera Error',
        'Failed to capture photo. Please try again.',
        [{ text: 'OK' }]
      );
    } finally {
      setIsCapturing(false);
      setIsProcessingPhoto(false);
    }
  };

  const finishCapture = () => {
    if (batchMode && capturedPhotos.length > 0) {
      // Return all captured photos
      setUri(capturedPhotos);
    } else {
      // Just go back without photos
      backPressed();
    }
  };

  const deletePhoto = (index) => {
    setCapturedPhotos(prev => prev.filter((_, i) => i !== index));
  };

  return (
    <View style={styles.container}>
      <CameraView 
        style={styles.camera} 
        facing={facing} 
        ref={cameraRef}
        onCameraReady={handleCameraReady}
        enableZoomGesture={false}
        enablePanGesture={false}
      >
        {/* Camera not ready overlay */}
        {!isCameraReady && (
          <View style={styles.cameraNotReadyOverlay}>
            <ActivityIndicator size="large" color="white" />
            <Text style={styles.cameraNotReadyText}>Camera initializing...</Text>
          </View>
        )}

        <TouchableOpacity 
          onPress={backPressed} 
          style={[styles.backButton, (!isCameraReady || isProcessingPhoto) && styles.disabledButton]}
          disabled={!isCameraReady || isProcessingPhoto}
        >
          <BlurView intensity={80} tint="dark" style={styles.blurView}>
            <AntDesign name="arrowleft" size={24} color="white" />
          </BlurView>
        </TouchableOpacity>

        {/* Photo Counter Badge (only in batch mode) */}
        {batchMode && capturedPhotos.length > 0 && (
          <TouchableOpacity
            style={styles.photoCounterBadge}
            onPress={() => setShowPreviewModal(true)}
            disabled={!isCameraReady || isProcessingPhoto}
          >
            <BlurView intensity={80} tint="dark" style={styles.badgeBlur}>
              <View style={styles.badgeContent}>
                <MaterialIcons name="photo-library" size={20} color="white" />
                <View style={styles.countBadge}>
                  <Text style={styles.countText}>{capturedPhotos.length}</Text>
                </View>
              </View>
            </BlurView>
          </TouchableOpacity>
        )}

        <View style={styles.controlsContainer}>
          <BlurView intensity={50} tint="dark" style={styles.controlsBlur}>

            <TouchableOpacity 
              style={[styles.flipButton, (!isCameraReady || isProcessingPhoto) && styles.disabledButton]} 
              onPress={toggleCameraFacing}
              disabled={!isCameraReady || isProcessingPhoto}
            >
              <Ionicons name="camera-reverse-outline" size={30} color="white" />
            </TouchableOpacity>

            <TouchableOpacity
              style={[
                styles.captureButton,
                (!isCameraReady || isProcessingPhoto || isCapturing) && styles.disabledCaptureButton
              ]}
              onPress={takePicture}
              disabled={!isCameraReady || isProcessingPhoto || isCapturing}
            >
              {isProcessingPhoto ? (
                <ActivityIndicator size="large" color="#0B36A1" />
              ) : (
                <View style={styles.captureButtonInner} />
              )}
            </TouchableOpacity>

            {/* Done button for batch mode, or placeholder for single mode */}
            {batchMode && capturedPhotos.length > 0 ? (
              <TouchableOpacity 
                style={[styles.doneButton, (!isCameraReady || isProcessingPhoto) && styles.disabledButton]} 
                onPress={finishCapture}
                disabled={!isCameraReady || isProcessingPhoto}
              >
                <MaterialIcons name="check" size={30} color="white" />
              </TouchableOpacity>
            ) : (
              <View style={styles.placeholderButton} />
            )}
          </BlurView>
        </View>
      </CameraView>

      {/* Photo Preview Modal */}
      <Modal
        visible={showPreviewModal}
        transparent={true}
        animationType="slide"
        onRequestClose={() => setShowPreviewModal(false)}
      >
        <View style={styles.modalOverlay}>
          <BlurView intensity={100} tint="dark" style={styles.modalBlur}>
            <View style={styles.modalContent}>
              <View style={styles.modalHeader}>
                <Text style={styles.modalTitle}>
                  {capturedPhotos.length} Photo{capturedPhotos.length !== 1 ? 's' : ''} Captured
                </Text>
                <TouchableOpacity
                  style={styles.modalCloseButton}
                  onPress={() => setShowPreviewModal(false)}
                >
                  <Ionicons name="close" size={24} color="white" />
                </TouchableOpacity>
              </View>

              <ScrollView
                style={styles.modalScrollView}
                contentContainerStyle={styles.modalScrollContent}
              >
                <View style={styles.photoGrid}>
                  {capturedPhotos.map((uri, index) => (
                    <View key={index} style={styles.modalPhotoContainer}>
                      <Image source={{ uri }} style={styles.modalPhoto} />
                      <TouchableOpacity
                        style={styles.modalDeleteButton}
                        onPress={() => deletePhoto(index)}
                      >
                        <Ionicons name="trash" size={20} color="white" />
                      </TouchableOpacity>
                      <View style={styles.photoNumber}>
                        <Text style={styles.photoNumberText}>{index + 1}</Text>
                      </View>
                    </View>
                  ))}
                </View>
              </ScrollView>

              <View style={styles.modalActions}>
                <TouchableOpacity
                  style={styles.modalActionButton}
                  onPress={() => setShowPreviewModal(false)}
                >
                  <Text style={styles.modalActionText}>Continue Taking Photos</Text>
                </TouchableOpacity>
                <TouchableOpacity
                  style={[styles.modalActionButton, styles.doneActionButton]}
                  onPress={() => {
                    setShowPreviewModal(false);
                    finishCapture();
                  }}
                >
                  <MaterialIcons name="check" size={20} color="white" />
                  <Text style={[styles.modalActionText, styles.doneActionText]}>Done</Text>
                </TouchableOpacity>
              </View>
            </View>
          </BlurView>
        </View>
      </Modal>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  camera: {
    flex: 1,
  },
  cameraNotReadyOverlay: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    backgroundColor: 'rgba(0,0,0,0.6)',
    justifyContent: 'center',
    alignItems: 'center',
    zIndex: 10,
  },
  cameraNotReadyText: {
    color: 'white',
    marginTop: 16,
    fontSize: 16,
    fontWeight: '600',
    textAlign: 'center',
  },

  permissionContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: '#f8f9fa',
    padding: 20,
  },
  permissionText: {
    fontSize: 18,
    textAlign: 'center',
    marginTop: 20,
    marginBottom: 30,
    color: '#343a40',
    fontFamily: 'Roboto',
  },
  permissionButton: {
    backgroundColor: '#228be6',
    paddingVertical: 12,
    paddingHorizontal: 30,
    borderRadius: 30,
    elevation: 2,
  },
  permissionButtonText: {
    color: 'white',
    fontSize: 16,
    fontWeight: '600',
  },

  backButton: {
    position: 'absolute',
    top: 50,
    left: 20,
    zIndex: 10,
  },
  disabledButton: {
    opacity: 0.5,
  },

  controlsContainer: {
    position: 'absolute',
    bottom: 40,
    left: 0,
    right: 0,
  },
  controlsBlur: {
    flexDirection: 'row',
    justifyContent: 'space-around',
    alignItems: 'center',
    margin: 20,
    borderRadius: 40,
    overflow: 'hidden',
    paddingVertical: 20,
  },

  flipButton: {
    width: 50,
    height: 50,
    borderRadius: 25,
    justifyContent: 'center',
    alignItems: 'center',
  },
  captureButton: {
    width: 80,
    height: 80,
    borderRadius: 40,
    backgroundColor: 'rgba(255, 255, 255, 0.3)',
    justifyContent: 'center',
    alignItems: 'center',
    borderWidth: 3,
    borderColor: 'white',
  },
  disabledCaptureButton: {
    opacity: 0.5,
    backgroundColor: 'rgba(255, 255, 255, 0.1)',
  },
  captureButtonInner: {
    width: 60,
    height: 60,
    borderRadius: 30,
    backgroundColor: 'white',
  },
  placeholderButton: {
    width: 50,
    height: 50,
  },
  doneButton: {
    width: 50,
    height: 50,
    borderRadius: 25,
    backgroundColor: 'rgba(76, 175, 80, 0.8)',
    justifyContent: 'center',
    alignItems: 'center',
    borderWidth: 2,
    borderColor: 'white',
  },

  photoCounterBadge: {
    position: 'absolute',
    top: 110, // Below the back button (back button is at 50px + 50px height + 10px gap)
    left: 20, // Same left position as back button
    zIndex: 5,
  },
  badgeBlur: {
    borderRadius: 30, // Match back button's blur radius
    overflow: 'hidden',
    padding: 12, // Match back button's padding
    minWidth: 50, // Ensure consistent size
    minHeight: 50,
    justifyContent: 'center',
    alignItems: 'center',
  },
  badgeContent: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    position: 'relative',
  },
  countBadge: {
    position: 'absolute',
    top: -8,
    right: -8,
    backgroundColor: '#FF6B6B',
    borderRadius: 12,
    minWidth: 24,
    height: 24,
    justifyContent: 'center',
    alignItems: 'center',
    borderWidth: 2,
    borderColor: 'white',
  },
  countText: {
    color: 'white',
    fontSize: 12,
    fontWeight: 'bold',
  },

  // Modal styles
  modalOverlay: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  modalBlur: {
    width: width * 0.9,
    maxHeight: height * 0.8,
    borderRadius: 20,
    overflow: 'hidden',
  },
  modalContent: {
    padding: 20,
  },
  modalHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 20,
  },
  modalTitle: {
    color: 'white',
    fontSize: 18,
    fontWeight: 'bold',
  },
  modalCloseButton: {
    padding: 5,
  },
  modalScrollView: {
    maxHeight: height * 0.5,
  },
  modalScrollContent: {
    paddingBottom: 10,
  },
  photoGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    justifyContent: 'space-between',
  },
  modalPhotoContainer: {
    position: 'relative',
    marginBottom: 15,
    width: (width * 0.9 - 60) / 2,
  },
  modalPhoto: {
    width: '100%',
    aspectRatio: 1,
    borderRadius: 12,
    borderWidth: 2,
    borderColor: 'white',
  },
  modalDeleteButton: {
    position: 'absolute',
    top: 8,
    right: 8,
    backgroundColor: 'rgba(255, 0, 0, 0.8)',
    borderRadius: 15,
    padding: 5,
  },
  photoNumber: {
    position: 'absolute',
    bottom: 8,
    left: 8,
    backgroundColor: 'rgba(0, 0, 0, 0.7)',
    borderRadius: 12,
    paddingHorizontal: 8,
    paddingVertical: 4,
  },
  photoNumberText: {
    color: 'white',
    fontSize: 12,
    fontWeight: 'bold',
  },
  modalActions: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginTop: 20,
    gap: 10,
  },
  modalActionButton: {
    flex: 1,
    backgroundColor: 'rgba(255, 255, 255, 0.2)',
    borderRadius: 12,
    padding: 15,
    alignItems: 'center',
    borderWidth: 1,
    borderColor: 'rgba(255, 255, 255, 0.3)',
  },
  doneActionButton: {
    backgroundColor: 'rgba(76, 175, 80, 0.8)',
    flexDirection: 'row',
    gap: 8,
  },
  modalActionText: {
    color: 'white',
    fontSize: 14,
    fontWeight: '600',
  },
  doneActionText: {
    color: 'white',
  },

  blurView: {
    borderRadius: 30,
    overflow: 'hidden',
    padding: 12, 
    minWidth: 50, 
    minHeight: 50,
    justifyContent: 'center',
    alignItems: 'center',
  },
  processingOverlay: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    backgroundColor: 'rgba(0,0,0,0.4)',
    justifyContent: 'center',
    alignItems: 'center',
    zIndex: 100,
  },
  processingText: {
    color: '#fff',
    marginTop: 16,
    fontSize: 18,
    fontWeight: 'bold',
  },
});

export default PhotoCapture;