import React from 'react';
import {
  View,
  Text,
  StyleSheet,
  Modal,
  ScrollView,
  TouchableOpacity,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';

const TagInfoModal = ({ 
  visible, 
  onClose, 
  tagInfo,
  formatDuration 
}) => {
  if (!tagInfo) return null;

  return (
    <Modal
      visible={visible}
      transparent={true}
      animationType="fade"
      onRequestClose={onClose}
    >
      <View style={styles.modalOverlay}>
        <View style={styles.modalContent}>
          <View style={styles.modalHeader}>
            <Text style={styles.modalTitle}>
              {tagInfo.type.split('_').map(word => 
                word.charAt(0).toUpperCase() + word.slice(1)
              ).join(' ')}
            </Text>
            <TouchableOpacity onPress={onClose}>
              <Ionicons name="close" size={24} color="#333" />
            </TouchableOpacity>
          </View>
          <ScrollView showsVerticalScrollIndicator={false} style={styles.modalBody}>
            
            <View style={styles.modalSection}>
              <Text style={styles.modalSectionTitle}>Submission Time</Text>
              <View style={styles.modalDetails}>
                <View style={styles.bulletPointContainer}>
                  <Text style={styles.bulletPoint}>•</Text>
                  <Text style={styles.modalText}>
                    Must be submitted within {formatDuration(tagInfo.durationInMinutesToSubmit)}
                  </Text>
                </View>
                <View style={styles.bulletPointContainer}>
                  <Text style={styles.bulletPoint}>•</Text>
                  <Text style={styles.modalText}>
                    Time starts from the moment of collection
                  </Text>
                </View>
                <View style={styles.bulletPointContainer}>
                  <Text style={styles.bulletPoint}>•</Text>
                  <Text style={styles.modalText}>
                    Late submissions may affect evidence validity
                  </Text>
                </View>
              </View>
            </View>

            {tagInfo.packagingAdvice && (
              <>
                <View style={styles.modalSection}>
                  <Text style={styles.modalSectionTitle}>Packaging Requirements</Text>
                  <View style={styles.modalDetails}>
                    <View style={styles.bulletPointContainer}>
                      <Text style={styles.bulletPoint}>•</Text>
                      <Text style={styles.modalText}>
                        Container Type: {tagInfo.packagingAdvice.containerType}
                      </Text>
                    </View>
                    <View style={styles.bulletPointContainer}>
                      <Text style={styles.bulletPoint}>•</Text>
                      <Text style={styles.modalText}>
                        Size: {tagInfo.packagingAdvice.containerSize}
                      </Text>
                    </View>
                    <View style={styles.bulletPointContainer}>
                      <Text style={styles.bulletPoint}>•</Text>
                      <Text style={styles.modalText}>
                        Material: {tagInfo.packagingAdvice.containerMaterial}
                      </Text>
                    </View>
                    <View style={styles.bulletPointContainer}>
                      <Text style={styles.bulletPoint}>•</Text>
                      <Text style={styles.modalText}>
                        Color: {tagInfo.packagingAdvice.containerColor}
                      </Text>
                    </View>
                    <View style={styles.bulletPointContainer}>
                      <Text style={styles.bulletPoint}>•</Text>
                      <Text style={styles.modalText}>
                        Dimensions: {tagInfo.packagingAdvice.containerDimensions}
                      </Text>
                    </View>
                    <View style={styles.bulletPointContainer}>
                      <Text style={styles.bulletPoint}>•</Text>
                      <Text style={styles.modalText}>
                        Quantity: {tagInfo.packagingAdvice.containerQuantity}
                      </Text>
                    </View>
                  </View>
                </View>

                <View style={[styles.modalSection, { marginBottom: 0 }]}>
                  <Text style={styles.modalSectionTitle}>Packaging Instructions</Text>
                  <View style={styles.modalDetails}>
                    <View style={styles.bulletPointContainer}>
                      <Text style={styles.bulletPoint}>•</Text>
                      <Text style={styles.modalText}>
                        Use {tagInfo.packagingAdvice.containerQuantity} {tagInfo.packagingAdvice.containerType}(s) of {tagInfo.packagingAdvice.containerSize} size
                      </Text>
                    </View>
                    <View style={styles.bulletPointContainer}>
                      <Text style={styles.bulletPoint}>•</Text>
                      <Text style={styles.modalText}>
                        Container should be made of {tagInfo.packagingAdvice.containerMaterial} and {tagInfo.packagingAdvice.containerColor} in color
                      </Text>
                    </View>
                    <View style={styles.bulletPointContainer}>
                      <Text style={styles.bulletPoint}>•</Text>
                      <Text style={styles.modalText}>
                        Each container should have dimensions of {tagInfo.packagingAdvice.containerDimensions}
                      </Text>
                    </View>
                    <View style={styles.bulletPointContainer}>
                      <Text style={styles.bulletPoint}>•</Text>
                      <Text style={styles.modalText}>
                        Ensure proper sealing and labeling of all containers
                      </Text>
                    </View>
                    <View style={styles.bulletPointContainer}>
                      <Text style={styles.bulletPoint}>•</Text>
                      <Text style={styles.modalText}>
                        Handle with care to prevent contamination or damage
                      </Text>
                    </View>
                  </View>
                </View>
              </>
            )}
          </ScrollView>
        </View>
      </View>
    </Modal>
  );
};

const styles = StyleSheet.create({
  modalOverlay: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    justifyContent: 'center',
    alignItems: 'center',
    zIndex: 1000,
  },
  modalContent: {
    backgroundColor: 'white',
    borderRadius: 12,
    padding: 20,
    width: '90%',
    maxWidth: 400,
    maxHeight: '80%',
    paddingBottom: 0,
  },
  modalHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    borderBottomWidth: 1,
    borderBottomColor: '#e0e0e0',
    paddingBottom: 12,
    marginBottom: 0,
  },
  modalTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#333',
  },
  modalBody: {
    marginTop: 16,
  },
  modalSection: {
    marginBottom: 16,
  },
  modalSectionTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: '#0B36A1',
    marginBottom: 8,
  },
  modalDetails: {
    backgroundColor: '#f8f8f8',
    padding: 12,
    borderRadius: 8,
  },
  modalText: {
    fontSize: 14,
    color: '#333',
    flex: 1,
    lineHeight: 20,
  },
  bulletPointContainer: {
    flexDirection: 'row',
    marginBottom: 8,
    paddingRight: 8,
  },
  bulletPoint: {
    fontSize: 14,
    color: '#333',
    marginRight: 8,
    width: 8,
  },
});

export default TagInfoModal; 