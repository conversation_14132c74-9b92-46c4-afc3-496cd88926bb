import React, { useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { Colors } from '../../constants/colors';
import TagInfoModal from '../Larges/TagInfoModal';

const EvidenceTagsSection = ({ 
  tagsOptions, 
  selectedTags, 
  onTagSelect,
  tagData 
}) => {
  const [selectedTagInfo, setSelectedTagInfo] = useState(null);
  const [showTooltip, setShowTooltip] = useState(false);

  const formatDuration = (minutes) => {
    const hours = Math.floor(minutes / 60);
    const remainingMinutes = minutes % 60;
    if (hours > 0) {
      return `${hours} hour${hours > 1 ? 's' : ''} ${remainingMinutes > 0 ? `and ${remainingMinutes} minute${remainingMinutes > 1 ? 's' : ''}` : ''}`;
    }
    return `${minutes} minute${minutes > 1 ? 's' : ''}`;
  };

  const getTagInfo = (tagType) => {
    const tag = tagsOptions.find(t => t.key === tagType);
    if (tag) {
      const tagDataItem = tagData.find(t => t.type === tagType);
      if (tagDataItem) {
        return {
          type: tag.value,
          packagingAdvice: tagDataItem.packagingAdvice,
          durationInMinutesToSubmit: tagDataItem.durationInMinutesToSubmit
        };
      }
    }
    return null;
  };

  return (
    <View style={styles.tagsContainer}>
      <Text style={styles.sectionTitle}>Evidence Tags</Text>
      <ScrollView 
        horizontal 
        showsHorizontalScrollIndicator={false}
        style={styles.tagsScrollView}
      >
        {tagsOptions.map((tag) => (
          <TouchableOpacity
            key={tag.key}
            style={[
              styles.tagCard,
              selectedTags.includes(tag.key) && styles.selectedTagCard
            ]}
            onPress={() => {
              if (selectedTags.includes(tag.key)) {
                onTagSelect(prev => prev.filter(t => t !== tag.key));
              } else {
                onTagSelect(prev => [...prev, tag.key]);
              }
            }}
          >
            <View style={styles.tagCardContent}>
              <Ionicons 
                name={selectedTags.includes(tag.key) ? "checkmark-circle" : "ellipse-outline"} 
                size={24} 
                color={selectedTags.includes(tag.key) ? Colors.primary : "#666"} 
              />
              <Text style={[
                styles.tagCardText,
                selectedTags.includes(tag.key) && styles.selectedTagCardText
              ]}>
                {tag.value}
              </Text>
              <TouchableOpacity
                onPress={() => {
                  const info = getTagInfo(tag.key);
                  if (info) {
                    setSelectedTagInfo(info);
                    setShowTooltip(true);
                  }
                }}
                style={styles.infoButton}
              >
                <Ionicons name="information-circle-outline" size={20} color={Colors.primary} />
              </TouchableOpacity>
            </View>
          </TouchableOpacity>
        ))}
      </ScrollView>

      {selectedTags.length > 0 && (
        <View style={styles.selectedTagsContainer}>
          <Text style={styles.selectedTagsTitle}>Selected Tags:</Text>
          <View style={styles.selectedTagsList}>
            {selectedTags.map((tagKey) => {
              const tag = tagsOptions.find(t => t.key === tagKey);
              return (
                <View key={tagKey} style={styles.selectedTagItem}>
                  <Text style={styles.selectedTagText}>{tag.value}</Text>
                  <TouchableOpacity 
                    onPress={() => onTagSelect(prev => prev.filter(t => t !== tagKey))}
                    style={styles.removeTagButton}
                  >
                    <Ionicons name="close-circle" size={18} color="#666" />
                  </TouchableOpacity>
                </View>
              );
            })}
          </View>
        </View>
      )}

      <TagInfoModal
        visible={showTooltip}
        onClose={() => setShowTooltip(false)}
        tagInfo={selectedTagInfo}
        formatDuration={formatDuration}
      />
    </View>
  );
};

const styles = StyleSheet.create({
  tagsContainer: {
    flex: 1,
  },
  sectionTitle: {
    fontSize: 14,
    fontFamily: 'Roboto_medium',
    color: Colors.black,
    marginBottom: 12,
  },
  tagsScrollView: {
    marginBottom: 12,
  },
  tagCard: {
    backgroundColor: '#f8f8f8',
    borderRadius: 30,
    paddingHorizontal: 10,
    marginRight: 10,
    borderWidth: 1,
    borderColor: '#e0e0e0',
    minWidth: 120,
    height: 40,
    justifyContent: 'center',
  },
  selectedTagCard: {
    backgroundColor: '#f0f7ff',
    borderColor: Colors.primary,
  },
  tagCardContent: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  tagCardText: {
    fontSize: 13,
    fontFamily: 'Roboto',
    color: Colors.black,
    marginLeft: 6,
  },
  selectedTagCardText: {
    color: Colors.primary,
    fontFamily: 'Roboto_medium',
  },
  infoButton: {
    marginLeft: 8,
    padding: 4,
  },
  selectedTagsContainer: {
    marginTop: 12,
    padding: 10,
    backgroundColor: '#f8f8f8',
    borderRadius: 8,
  },
  selectedTagsTitle: {
    fontSize: 14,
    fontFamily: 'Roboto_medium',
    color: Colors.black,
    marginBottom: 8,
  },
  selectedTagsList: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: 6,
  },
  selectedTagItem: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: Colors.primary,
    borderRadius: 16,
    paddingHorizontal: 10,
    paddingVertical: 4,
  },
  selectedTagText: {
    color: 'white',
    fontSize: 13,
    fontFamily: 'Roboto',
    marginRight: 4,
  },
  removeTagButton: {
    padding: 2,
  },
});

export default EvidenceTagsSection; 