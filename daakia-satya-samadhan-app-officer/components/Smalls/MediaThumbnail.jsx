import React from 'react';
import { View, Image, TouchableOpacity, StyleSheet } from 'react-native';
import { MaterialIcons, AntDesign } from '@expo/vector-icons';

const MediaThumbnail = ({ uri, type, compressed, onDelete, thumbnailSize }) => {
  return (
    <View style={[styles.mediaItem, { width: thumbnailSize, height: thumbnailSize }]}>
      <Image source={{ uri }} style={styles.thumbnail} resizeMode="cover" />
      {type === 'video' && (
        <MaterialIcons
          name="videocam"
          size={24}
          color="white"
          style={styles.mediaTypeIcon}
        />
      )}
      {compressed && (
        <MaterialIcons
          name="compress"
          size={20}
          color="white"
          style={[styles.mediaTypeIcon, { right: 5, left: undefined }]}
        />
      )}
      <TouchableOpacity style={styles.deleteButton} onPress={onDelete}>
        <AntDesign name="closecircle" size={20} color="red" />
      </TouchableOpacity>
    </View>
  );
};

const styles = StyleSheet.create({
  mediaItem: {
    borderRadius: 8,
    overflow: 'hidden',
    margin: 5,
    position: 'relative',
  },
  thumbnail: {
    width: '100%',
    height: '100%',
    resizeMode: 'cover',
  },
  mediaTypeIcon: {
    position: 'absolute',
    bottom: 5,
    left: 5,
    backgroundColor: 'rgba(0,0,0,0.5)',
    borderRadius: 12,
    padding: 2,
  },
  deleteButton: {
    position: 'absolute',
    top: 5,
    right: 5,
    backgroundColor: 'white',
    borderRadius: 12,
  },
});

export default MediaThumbnail;