import { AUTH_ERRORS } from '../constants/auth';

export const STATUS = {
  INITIATED: "initiated",
  DISPATCHED: "dispatched",
  RECEIVED: "received",
  REJECTED: "rejected",
  COMPLETED: "completed",
};

// Status display names mapping
export const STATUS_DISPLAY_NAMES = {
  [STATUS.INITIATED]: "Dispatched to Malkhana",
  [STATUS.DISPATCHED]: "Dispatched to Lab",
  [STATUS.RECEIVED]: "Accepted in Lab",
  [STATUS.REJECTED]: "Rejected by Lab",
  [STATUS.COMPLETED]: "Report Generated",
};

// Function to get display name for status
export const getStatusDisplayName = (status) => {
  return STATUS_DISPLAY_NAMES[status] || status;
};

// Function to get forensic status for evidence
export const getForensicStatus = (evidence) => {
  const forensicRequests = evidence.forensicRequests || [];

  // Case 1: Empty array - not yet dispatched
  if (forensicRequests.length === 0) {
    return {
      status: null,
      displayName: "Not yet Dispatched",
      canSend: true
    };
  }

  // Case 2: Get the latest/most recent forensic request
  const latestRequest = forensicRequests[forensicRequests.length - 1];

  return {
    status: latestRequest.status,
    displayName: getStatusDisplayName(latestRequest.status),
    canSend: latestRequest.status === STATUS.REJECTED || latestRequest.status === STATUS.COMPLETED
  };
};

export const validationService = {
  validateMobile: (mobileNumber) => {
    if (!mobileNumber || mobileNumber.length !== 10) {
      throw new Error(AUTH_ERRORS.INVALID_MOBILE);
    }
  },

  validateOtp: (otp) => {
    if (otp.length !== 5) {
      throw new Error(AUTH_ERRORS.INVALID_OTP);
    }
  },

  validateEmail: (email) => {
    if (email && !/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(email)) {
      throw new Error(AUTH_ERRORS.INVALID_EMAIL);
    }
  },

  validateName: (name) => {
    if (name && name.trim().length < 2) {
      throw new Error(AUTH_ERRORS.INVALID_NAME);
    }
  },

  validateAadhar: (aadhar) => {
    if (!aadhar || aadhar.length !== 12 || isNaN(aadhar)) {
      throw new Error(AUTH_ERRORS.INVALID_AADHAR);
    }
  },

  validateProfileUpdate: (updateData) => {
    const allowedFields = ['name', 'emailId', 'mobileNumber', 'displayUrl'];
    const filteredData = {};
    
    allowedFields.forEach(field => {
      if (updateData[field] !== undefined) {
        filteredData[field] = updateData[field];
      }
    });

    if (filteredData.emailId) {
      validationService.validateEmail(filteredData.emailId);
    }

    if (filteredData.mobileNumber) {
      validationService.validateMobile(filteredData.mobileNumber);
    }

    if (filteredData.name) {
      validationService.validateName(filteredData.name);
    }

    return filteredData;
  },

  validateRegistration: (data) => {
    const { name, mobileNumber, aadhar, emailId } = data;

    validationService.validateName(name);
    validationService.validateMobile(mobileNumber);
    validationService.validateAadhar(aadhar);
    validationService.validateEmail(emailId);

    return data;
  },

  // Check if evidence can be sent to forensic labs
  canSendToForensic: (evidence) => {
    // Check if evidence has lab department info
    if (!evidence.lab_department || evidence.lab_department.length === 0) {
      return {
        canSend: false,
        reason: 'Evidence must have lab and department information before sending to forensic.'
      };
    }

    // Check forensic requests
    const forensicRequests = evidence.forensicRequests || [];

    // Case 1: No forensic requests - can send
    if (forensicRequests.length === 0) {
      return {
        canSend: true,
        reason: 'No previous forensic requests found.'
      };
    }

    // Case 2: Check if any forensic request has "rejected" status - can send
    const hasRejectedRequest = forensicRequests.some(request =>
      request.status === STATUS.REJECTED
    );

    if (hasRejectedRequest) {
      return {
        canSend: true,
        reason: 'Previous forensic request was rejected, can resend.'
      };
    }

    // Case 3: Check if all requests are in final states (completed)
    const allCompleted = forensicRequests.every(request =>
      request.status === STATUS.COMPLETED
    );

    if (allCompleted) {
      return {
        canSend: true,
        reason: 'All previous forensic requests are completed, can send new request.'
      };
    }

    // Case 4: Has active requests (initiated, dispatched, received) - cannot send
    const activeStatuses = [STATUS.INITIATED, STATUS.DISPATCHED, STATUS.RECEIVED];
    const hasActiveRequest = forensicRequests.some(request =>
      activeStatuses.includes(request.status)
    );

    if (hasActiveRequest) {
      return {
        canSend: false,
        reason: 'Evidence has active forensic requests. Cannot send until current requests are completed or rejected.'
      };
    }

    // Default case - allow sending
    return {
      canSend: true,
      reason: 'Evidence can be sent to forensic.'
    };
  },
};